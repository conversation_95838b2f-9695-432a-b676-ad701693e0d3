import numpy as np
import numba
import matplotlib.pyplot as plt
from scipy.optimize import brentq
import copy

# --- <PERSON>rid and Markov process functions ---
def discretize_assets(amin, amax, n_a):
    """Creates a non-linearly spaced grid for assets, denser at lower values."""
    ubar = np.log(1 + np.log(1 + amax - amin))
    u_grid = np.linspace(0, ubar, n_a)
    return amin + np.exp(np.exp(u_grid) - 1) - 1

def rouwenhorst_Pi(N, p_rw):
    """Constructs a Rouwenhorst approximation for an AR(1) process."""
    if N == 1: return np.array([[1.0]])
    Pi = np.array([[p_rw, 1 - p_rw], [1 - p_rw, p_rw]])
    for n_iter in range(3, N + 1):
        Pi_old = Pi; Pi = np.zeros((n_iter, n_iter))
        Pi[:-1, :-1] += p_rw * Pi_old; Pi[:-1, 1:] += (1 - p_rw) * Pi_old
        Pi[1:, :-1] += (1 - p_rw) * Pi_old; Pi[1:, 1:] += p_rw * Pi_old
        Pi[1:-1, :] /= 2
    return Pi

def stationary_markov(Pi_matrix, tol=1E-14):
    """Finds the stationary distribution of a Markov chain."""
    n_s = Pi_matrix.shape[0]
    if n_s == 1: return np.array([1.0])
    pi_dist = np.full(n_s, 1/n_s)
    for _ in range(10_000):
        pi_new = Pi_matrix.T @ pi_dist
        if np.max(np.abs(pi_new - pi_dist)) < tol: return pi_new
        pi_dist = pi_new
    raise ValueError("Stationary distribution did not converge")

def discretize_income(rho_z, sigma_val_z, n_z):
    """Discretizes the income process using Rouwenhorst's method."""
    p_rw = (1 + rho_z) / 2
    if n_z == 1: z_grid_unscaled = np.array([0.0])
    else:
        z_grid_unscaled = np.arange(n_z)
        alpha_scale = 2 * sigma_val_z / np.sqrt(n_z - 1) if n_z > 1 else 0.0
        z_grid_unscaled = alpha_scale * (z_grid_unscaled - (n_z - 1) / 2.0)
    
    Pi_z_matrix = rouwenhorst_Pi(n_z, p_rw)
    pi_z_dist = stationary_markov(Pi_z_matrix)
    z_values = np.exp(z_grid_unscaled)
    current_mean = np.vdot(pi_z_dist, z_values)
    
    if abs(current_mean) > 1e-12: z_values /= current_mean
    elif n_z == 1: z_values = np.array([1.0])
    
    return z_values, pi_z_dist, Pi_z_matrix

# --- Household problem helper functions ---
def P_C_func(C_val, p_goods, eps_goods, omega_goods, sigma_agg_param):
    """Price index function P(C) for a given composite consumption C."""
    if sigma_agg_param == 1: raise ValueError("sigma_agg = 1 not directly handled here.")
    C_val_safe = np.maximum(C_val, 1e-10)
    p_goods_safe = np.maximum(p_goods, 1e-12 if (1 - sigma_agg_param) % 1 != 0 else 0.0)
    
    sum_term = np.sum(omega_goods * (C_val_safe**eps_goods * p_goods_safe)**(1 - sigma_agg_param))
    exponent_on_sum = 1 / (1 - sigma_agg_param)

    if sum_term <= 1e-12 :
        if exponent_on_sum < 0:
            return (1e-12)**exponent_on_sum
        else:
            return 0.0
            
    val = sum_term**exponent_on_sum
    if not np.isfinite(val) or val < 1e-9 : return 1e-9
    return val

def P_prime_C_func(C_val, p_goods, eps_goods, omega_goods, sigma_agg_param):
    """Derivative of the price index function, P'(C)."""
    if sigma_agg_param == 1: raise ValueError("sigma_agg = 1 not directly handled here.")
    C_val_safe = np.maximum(C_val, 1e-10)
    p_goods_safe = np.maximum(p_goods, 1e-12 if (1 - sigma_agg_param) % 1 != 0 else 0.0)
    
    term_in_sum_e = omega_goods * (C_val_safe**eps_goods * p_goods_safe)**(1 - sigma_agg_param)
    sum_for_e = np.sum(term_in_sum_e)
    power_val = sigma_agg_param / (1-sigma_agg_param)

    if sum_for_e <= 1e-12:
        if power_val < 0: factor1 = (1e-12)**power_val
        elif power_val == 0: factor1 = 1.0
        else: factor1 = 0.0
    else:
        factor1 = sum_for_e**power_val

    sum_for_P_prime_terms = term_in_sum_e * eps_goods / np.maximum(C_val_safe, 1e-12)
    sum_for_P_prime_terms = np.nan_to_num(sum_for_P_prime_terms, nan=0.0, posinf=1e12, neginf=-1e12)
    sum_for_P_prime = np.sum(sum_for_P_prime_terms)
    P_prime = factor1 * sum_for_P_prime

    if not np.isfinite(P_prime) or P_prime < 1e-9:
        return max(1e-9, C_val_safe**(-abs(sigma_agg_param if sigma_agg_param !=0 else 1.0)))
    return P_prime

def P_inv_C_func(e_val, p_goods, eps_goods, omega_goods, sigma_agg_param, C_min_val=1e-8, C_max_val=1000.0, solver_xtol=1e-7, solver_rtol=1e-7):
    """Inverts the P(C) function to find C for a given expenditure e."""
    if sigma_agg_param == 1: raise ValueError("sigma_agg = 1 not directly handled here.")

    if e_val < P_C_func(C_min_val, p_goods, eps_goods, omega_goods, sigma_agg_param):
        return C_min_val

    def P_C_minus_e(C_val_root):
        if C_val_root <= 0: return 1e12
        val = P_C_func(C_val_root, p_goods, eps_goods, omega_goods, sigma_agg_param) - e_val
        return val

    try:
        f_min_initial = P_C_minus_e(C_min_val)
        f_max_initial = P_C_minus_e(C_max_val)
        
        if e_val < P_C_func(C_min_val * 1.001, p_goods, eps_goods, omega_goods, sigma_agg_param): 
            return C_min_val
        if e_val > P_C_func(C_max_val * 0.999, p_goods, eps_goods, omega_goods, sigma_agg_param): 
            return C_max_val

        if np.sign(f_min_initial) == np.sign(f_max_initial):
            if abs(f_min_initial) < 1e-3 : return C_min_val
            if abs(f_max_initial) < 1e-3 : return C_max_val
            # Fallback for root-finding failure
            avg_p_weighted = np.sum(p_goods * omega_goods) / np.sum(omega_goods) if np.sum(omega_goods)>0 else np.mean(p_goods)
            avg_p_weighted = max(avg_p_weighted, 1e-3)
            C_fallback = e_val / avg_p_weighted 
            return np.clip(C_fallback, C_min_val, C_max_val)

        return brentq(P_C_minus_e, C_min_val, C_max_val, xtol=solver_xtol, rtol=solver_rtol)
    except ValueError:
        if abs(P_C_minus_e(C_min_val)) < 1e-4 : return C_min_val
        if abs(P_C_minus_e(C_max_val)) < 1e-4 : return C_max_val
        # Fallback for root-finding failure
        avg_p_weighted = np.sum(p_goods * omega_goods) / np.sum(omega_goods) if np.sum(omega_goods)>0 else np.mean(p_goods)
        avg_p_weighted = max(avg_p_weighted, 1e-3)
        C_fallback = e_val / avg_p_weighted
        return np.clip(C_fallback, C_min_val, C_max_val)
    except Exception: raise

def get_good_consumptions(C_val, e_val, p_goods, eps_goods, omega_goods, sigma_agg_param):
    """Calculates consumption of each individual good c_i."""
    e_val_safe = np.maximum(e_val, 1e-12); C_val_safe = np.maximum(C_val, 1e-10)
    p_goods_safe = np.maximum(p_goods, 1e-9)
    
    price_ratio = p_goods_safe / e_val_safe
    price_ratio_safe = np.where(price_ratio > 1e-12, price_ratio, 1e-12)
    
    exponent_on_C = eps_goods * (1-sigma_agg_param)
    C_term = np.zeros_like(exponent_on_C)
    for i in range(len(exponent_on_C)):
        if C_val_safe < 1e-6 and exponent_on_C[i] < 0: C_term[i] = (1e-6)**exponent_on_C[i]
        elif C_val_safe < 1e-9 and exponent_on_C[i] > 0: C_term[i] = 0.0
        else: C_term[i] = C_val_safe**exponent_on_C[i]
            
    term_c_it = omega_goods * (price_ratio_safe)**(-sigma_agg_param) * C_term
    
    # Rescale to ensure budget constraint holds
    current_expenditure_check = np.sum(p_goods_safe * term_c_it)
    if current_expenditure_check > 1e-9 and abs(current_expenditure_check - e_val_safe)/e_val_safe > 0.001:
        term_c_it = term_c_it * (e_val_safe / current_expenditure_check)

    return np.nan_to_num(term_c_it, nan=1e-10, posinf=e_val_safe/(np.sum(p_goods_safe) if np.sum(p_goods_safe)>1e-9 else 1.0), neginf=1e-10 )

def backward_iteration_ge_corrected(Lambda_next_period, Pi_z_matrix, a_grid, z_grid, w_wage, r_rate,
                                    p_goods, eps_goods, omega_goods, sigma_agg_param,
                                    beta_param, theta_param, model_params_dict):
    """Performs one step of the backward iteration (EGM)."""
    if not np.all(np.isfinite(Lambda_next_period)):
        Lambda_next_period = np.nan_to_num(Lambda_next_period, nan=1.0, posinf=1e7, neginf=1e-7)
    Lambda_next_period = np.maximum(Lambda_next_period, 1e-7)

    # 1. Compute expected marginal value of savings
    X_t = beta_param * (1 + r_rate) * (Pi_z_matrix @ Lambda_next_period)

    # 2. Endogenous grid method: find C for each (z, a')
    C_endog = np.empty_like(X_t)
    C_min_solver_egm = model_params_dict.get('C_min_solver_egm', 1e-7)
    C_max_solver_egm = model_params_dict.get('C_max_solver_egm', 500.0)

    for z_idx in range(len(z_grid)):
        for a_idx_next in range(len(a_grid)):
            target_X = X_t[z_idx, a_idx_next]
            if not np.isfinite(target_X):
                C_endog[z_idx, a_idx_next] = C_min_solver_egm
                continue
            if target_X < 1e-9:
                C_endog[z_idx, a_idx_next] = C_min_solver_egm if target_X >=0 else C_max_solver_egm
                continue

            def egm_foc_resid(C_val_root):
                if C_val_root <= C_min_solver_egm/2: return 1e12 
                P_prime_val = P_prime_C_func(C_val_root, p_goods, eps_goods, omega_goods, sigma_agg_param)
                if P_prime_val < 1e-9 : return C_val_root**(-theta_param) 
                return C_val_root**(-theta_param) - target_X * P_prime_val

            try:
                f_min_egm = egm_foc_resid(C_min_solver_egm)
                f_max_egm = egm_foc_resid(C_max_solver_egm)
                if np.sign(f_min_egm) == np.sign(f_max_egm):
                    if abs(f_min_egm) < abs(f_max_egm) and abs(f_min_egm) < 1e-2: 
                        C_endog[z_idx, a_idx_next] = C_min_solver_egm
                    elif abs(f_max_egm) < abs(f_min_egm) and abs(f_max_egm) < 1e-2: 
                        C_endog[z_idx, a_idx_next] = C_max_solver_egm
                    else: 
                        C_endog[z_idx, a_idx_next] = np.sqrt(C_min_solver_egm * C_max_solver_egm) 
                    continue
                C_endog[z_idx, a_idx_next] = brentq(egm_foc_resid, C_min_solver_egm, C_max_solver_egm, xtol=1e-6, rtol=1e-6)
            except (ValueError, RuntimeError): 
                C_endog[z_idx, a_idx_next] = np.sqrt(C_min_solver_egm * C_max_solver_egm) 

    # 3. Find corresponding expenditure and cash-on-hand
    C_endog = np.maximum(C_endog, model_params_dict.get('C_floor', 1e-10))
    e_endog = np.array([[P_C_func(C_endog[zi,ai], p_goods, eps_goods, omega_goods, sigma_agg_param) for ai in range(len(a_grid))] for zi in range(len(z_grid))])
    e_endog = np.maximum(e_endog, model_params_dict.get('e_floor', 1e-9))
    coh_endog = e_endog + a_grid

    # 4. Interpolate to find asset policy on exogenous grid
    coh_exog = (1 + r_rate) * a_grid[np.newaxis, :] + z_grid[:, np.newaxis] * w_wage
    coh_exog = np.maximum(coh_exog, np.min(a_grid) + model_params_dict.get('e_floor', 1e-9) + 1e-7)
    a_policy = np.empty_like(coh_exog)

    for z_idx in range(len(z_grid)):
        coh_endog_z = coh_endog[z_idx, :]
        a_prime_choices_for_coh_endog_z = a_grid
        
        sorted_indices = np.argsort(coh_endog_z)
        coh_endog_z_sorted = coh_endog_z[sorted_indices]
        a_prime_sorted_for_interp = a_prime_choices_for_coh_endog_z[sorted_indices]
        
        unique_coh, unique_indices = np.unique(coh_endog_z_sorted, return_index=True)
        unique_a_prime_for_interp = a_prime_sorted_for_interp[unique_indices]

        if len(unique_coh) < 2: 
            if len(unique_coh) == 1: a_policy[z_idx, :] = unique_a_prime_for_interp[0]
            else: a_policy[z_idx, :] = a_grid[0] 
        else:
            a_policy[z_idx, :] = np.interp(coh_exog[z_idx, :], 
                                           unique_coh, unique_a_prime_for_interp, 
                                           left=unique_a_prime_for_interp[0],
                                           right=unique_a_prime_for_interp[-1])
    
    a_policy = np.maximum(a_policy, a_grid[0])
    a_policy = np.minimum(a_policy, a_grid[-1])

    # 5. Recover C and e policies
    e_policy = coh_exog - a_policy
    e_policy = np.maximum(e_policy, model_params_dict.get('e_floor',1e-9))
    C_policy = np.empty_like(e_policy)
    C_min_solver_policy = model_params_dict.get('C_min_solver_policy', 1e-7)
    C_max_solver_policy = model_params_dict.get('C_max_solver_policy', 500.0)

    for z_idx in range(len(z_grid)):
        for a_idx in range(len(a_grid)):
            C_policy[z_idx, a_idx] = P_inv_C_func(e_policy[z_idx, a_idx], p_goods, eps_goods, omega_goods, sigma_agg_param,
                                                 C_min_val=C_min_solver_policy, C_max_val=C_max_solver_policy,
                                                 solver_xtol=1e-6, solver_rtol=1e-6)
    C_policy = np.maximum(C_policy, model_params_dict.get('C_floor', 1e-10))

    # 6. Update marginal value of wealth Lambda
    Lambda_current = np.empty_like(C_policy)
    for z_idx,a_idx in np.ndindex(C_policy.shape):
        C_val = C_policy[z_idx, a_idx]
        P_prime_val = P_prime_C_func(C_val, p_goods, eps_goods, omega_goods, sigma_agg_param)
        
        if P_prime_val < 1e-9: 
            Lambda_current[z_idx, a_idx] = (C_val**(-theta_param)) / 1e-9 
        else:
            Lambda_current[z_idx, a_idx] = (C_val**(-theta_param)) / P_prime_val
        
        if not np.isfinite(Lambda_current[z_idx,a_idx]):
            Lambda_current[z_idx,a_idx] = np.nan_to_num( (C_val**(-theta_param)) / 1e-9, nan=1e7, posinf=1e7, neginf=1e-7)
    
    if not np.all(np.isfinite(Lambda_current)):
        Lambda_current = np.nan_to_num(Lambda_current, nan=1.0, posinf=1e7, neginf=1e-7)
    Lambda_current = np.maximum(Lambda_current, 1e-7)

    return Lambda_current, a_policy, C_policy, e_policy

def policy_ss_ge_corrected(Pi_z_matrix, a_grid, z_grid, w_wage, r_rate, p_goods, eps_goods, omega_goods, sigma_agg_param, beta_param, theta_param, model_params_dict, tol=1E-7, max_iter=15000):
    """Solves for the steady-state household policy functions by iterating on the Bellman equation."""
    # Smart initial guess for Lambda
    coh_guess = (z_grid[:,np.newaxis]*w_wage + (1+r_rate)*a_grid[np.newaxis,:])
    min_coh_val_practical = (np.min(z_grid)*w_wage if w_wage > 0 and z_grid.size > 0 and np.min(z_grid) > 0 else 0.0) + (1+r_rate)*a_grid[0]
    min_coh_val_practical = max(min_coh_val_practical, model_params_dict.get('e_floor', 1e-9) + a_grid[0] + 1e-7)
    coh_guess = np.maximum(coh_guess, min_coh_val_practical)
    
    C_init_guess = 0.10 * coh_guess
    C_init_guess = np.maximum(C_init_guess, model_params_dict.get('C_min_solver_policy', 1e-7))
    C_init_guess = np.minimum(C_init_guess, model_params_dict.get('C_max_solver_policy', 500.0))

    Lambda_init = (C_init_guess**(-theta_param)) # A simplified guess, ignoring P'
    Lambda_init = np.maximum(Lambda_init, 1e-7)
    
    Lambda = Lambda_init
    a_policy_old = np.zeros_like(Lambda)
    for it in range(max_iter):
        Lambda_new, a_policy, C_policy, e_policy = backward_iteration_ge_corrected(
            Lambda, Pi_z_matrix, a_grid, z_grid, w_wage, r_rate,
            p_goods, eps_goods, omega_goods, sigma_agg_param,
            beta_param, theta_param, model_params_dict
        )
        diff = np.max(np.abs(a_policy - a_policy_old))
        
        if it > 0 and diff < tol: 
            return Lambda_new, a_policy, C_policy, e_policy

        Lambda = Lambda_new
        a_policy_old = a_policy.copy()
        
    return Lambda, a_policy, C_policy, e_policy

def get_lottery(a_pol_m, a_gr_v):
    """Calculates the lottery for asset transitions to keep agents on the grid."""
    a_pol_m_clipped = np.clip(a_pol_m, a_gr_v[0], a_gr_v[-1])
    a_i = np.searchsorted(a_gr_v, a_pol_m_clipped, side='right') - 1
    a_i = np.maximum(0, np.minimum(a_i, len(a_gr_v) - 2))
    
    denom = a_gr_v[a_i+1] - a_gr_v[a_i]
    denom_safe = np.where(denom <= 1e-12, 1.0, denom)
    
    a_pi_m = (a_gr_v[a_i+1] - a_pol_m_clipped) / denom_safe
    a_pi_m = np.where(denom <= 1e-12, np.where(np.abs(a_pol_m_clipped - a_gr_v[a_i]) < 1e-9, 1.0, 0.0), a_pi_m)
    a_pi_m = np.maximum(0.0, np.minimum(1.0, a_pi_m))
    
    return a_i.astype(np.int32), a_pi_m

@numba.njit
def forward_policy_numba(D_m, a_i_m, a_pi_m, n_z_v, n_a_v):
    """Numba-jitted function to speed up forward iteration step."""
    Dend = np.zeros((n_z_v, n_a_v))
    for zi in range(n_z_v):
        for ai in range(n_a_v):
            mass = D_m[zi, ai]
            if mass < 1e-20: continue
            
            idx_low = a_i_m[zi, ai]
            prob_low = a_pi_m[zi, ai]
            prob_high = 1.0 - prob_low
            
            Dend[zi, idx_low] += prob_low * mass
            Dend[zi, idx_low + 1] += prob_high * mass
            
    return Dend

def forward_iteration(D_m, Pi_z_m, a_pol_m, a_gr_v, n_z_v, n_a_v):
    """Computes one step of the forward iteration for the distribution."""
    a_i_m, a_pi_m = get_lottery(a_pol_m, a_gr_v)
    Dend_prime = forward_policy_numba(D_m, a_i_m, a_pi_m, n_z_v, n_a_v)
    D_new = Pi_z_m.T @ Dend_prime
    return D_new

def distribution_ss(Pi_z_m, a_pol_m, a_gr_v, pi_z_d_init, tol=1E-9, max_iter=25000):
    """Solves for the stationary distribution of agents."""
    n_z_v, n_a_v = Pi_z_m.shape[0], len(a_gr_v)
    D_m = pi_z_d_init[:, None] * (np.ones(n_a_v) / n_a_v)
    if not np.isclose(np.sum(D_m),1.0): D_m = D_m / np.sum(D_m)

    for it in range(max_iter):
        D_new = forward_iteration(D_m, Pi_z_m, a_pol_m, a_gr_v, n_z_v, n_a_v)
        if np.max(np.abs(D_new - D_m)) < tol:
            if not np.isclose(np.sum(D_new),1.0): D_new = D_new / np.sum(D_new)
            return D_new
        D_m = D_new
        
    if not np.isclose(np.sum(D_m),1.0): D_m = D_m / np.sum(D_m)
    return D_m

# --- Firm side and GE functions ---
def get_static_prices_and_kl_ratios(r_rate, model_params_t):
    """Calculates firm-side prices (w, p_i) and K/L ratios given r."""
    delta = model_params_t['delta']; A_g = model_params_t['A_goods']; alpha_g = model_params_t['alpha_goods']
    
    user_cost_capital = r_rate + delta
    if user_cost_capital <= 1e-9:
        raise ValueError(f"r+delta non-positive ({user_cost_capital:.3e}) for r={r_rate:.3e}.")
    if alpha_g[0] == 0:
        raise ValueError(f"alpha_g[0] is zero, K/L ratio calculation for sector 0 is ill-defined.")
    
    # Sector 0 is numeraire, solve for w and K/L_0
    kl_ratio_a_base_num = (1-alpha_g[0]) * A_g[0]
    kl_ratio_a_base_den = user_cost_capital
    kl_ratio_a = (kl_ratio_a_base_num / kl_ratio_a_base_den)**(1/alpha_g[0])
    
    if kl_ratio_a <= 0 or not np.isfinite(kl_ratio_a):
        raise ValueError(f"kl_ratio_a non-positive/finite ({kl_ratio_a:.3e}).")
    
    w_wage = user_cost_capital * kl_ratio_a * alpha_g[0] / (1-alpha_g[0])
    if w_wage <= 0 or not np.isfinite(w_wage):
        raise ValueError(f"w_wage non-positive/finite ({w_wage:.3e}).")

    # Solve for prices and K/L in other sectors
    p_goods_v = np.ones(len(A_g)); kl_ratios_v = np.zeros(len(A_g)); kl_ratios_v[0] = kl_ratio_a
    for i in range(1, len(A_g)):
        if abs(alpha_g[i]) < 1e-9:
             raise ValueError(f"alpha_g[{i}] is zero for non-numeraire sector.")
        
        kl_num = w_wage * (1-alpha_g[i]) 
        kl_den = user_cost_capital * alpha_g[i] 
        if kl_den <= 0 : raise ValueError(f"KL ratio den non-positive ({kl_den:.2e}) for sector {i}")
        kl_ratios_v[i] = kl_num/kl_den
        if kl_ratios_v[i] <=0 or not np.isfinite(kl_ratios_v[i]): 
             raise ValueError(f"kl_ratio sector {i} non-positive/finite ({kl_ratios_v[i]:.2e}).")

        denom_p_base = kl_ratios_v[i]**(1-alpha_g[i])
        denom_p = (A_g[i] * alpha_g[i] * denom_p_base)
        if abs(denom_p) < 1e-12:
            p_goods_v[i] = 1e12 if w_wage > 1e-9 else 1.0
        else: 
            p_goods_v[i] = w_wage/denom_p
        if p_goods_v[i] <= 0 or not np.isfinite(p_goods_v[i]): 
            raise ValueError(f"p_good sector {i} non-positive/finite ({p_goods_v[i]:.2e}).")
            
    return w_wage, p_goods_v, kl_ratios_v

def get_firm_factor_demands_and_prod(C_agg_g_t, K_supply_t, p_gds_t, delta_val, kl_ratios_t, model_params_t):
    """Calculates sectoral and aggregate factor demands and production."""
    A_g_v = model_params_t['A_goods']; alpha_g_v = model_params_t['alpha_goods']
    
    E_cons_val = np.sum(p_gds_t * C_agg_g_t)
    E_cons_val = max(E_cons_val, 0.0)
    K_target_next_period = model_params_t.get('K_next_for_firms', K_supply_t)
    Investment_value = K_target_next_period - (1-delta_val)*K_supply_t
    Total_value_demand = E_cons_val + (1/3)*Investment_value

    Y_i_prod_v = np.zeros_like(C_agg_g_t); K_demand_sec = np.zeros_like(Y_i_prod_v); L_demand_sec = np.zeros_like(Y_i_prod_v)
    if Total_value_demand <= 1e-9:
        Y_i_prod_v.fill(1e-10)
    else:
        if E_cons_val > 1e-9:
            scaling_factor_Y = Total_value_demand / E_cons_val
            Y_i_prod_v = C_agg_g_t * scaling_factor_Y
        else: # If consumption is zero, allocate based on prices
            safe_p_gds_t = np.maximum(p_gds_t, 1e-9)
            Y_i_prod_v = (Total_value_demand / len(C_agg_g_t)) / safe_p_gds_t
            
    Y_i_prod_v = np.maximum(Y_i_prod_v, 1e-10)
    
    for i in range(len(Y_i_prod_v)):
        if Y_i_prod_v[i] < 1e-9: 
            L_demand_sec[i] = 0.0; K_demand_sec[i] = 0.0; continue
        if A_g_v[i] < 1e-9:
            L_demand_sec[i] = 1e12 if Y_i_prod_v[i] > 1e-9 else 0.0
            K_demand_sec[i] = kl_ratios_t[i] * L_demand_sec[i]
            continue
            
        denom_L = (A_g_v[i] * kl_ratios_t[i]**(1-alpha_g_v[i]))
        if abs(denom_L) < 1e-12 or not np.isfinite(denom_L):
            L_demand_sec[i] = 1e12 if Y_i_prod_v[i] > 1e-9 else 0.0
        else:
            L_demand_sec[i] = Y_i_prod_v[i]/denom_L
        
        L_demand_sec[i] = np.clip(L_demand_sec[i], 0, 1e12) 
        K_demand_sec[i] = kl_ratios_t[i] * L_demand_sec[i] 
        K_demand_sec[i] = np.clip(K_demand_sec[i], 0, 1e12 * np.max(kl_ratios_t if kl_ratios_t.size >0 and np.all(np.isfinite(kl_ratios_t)) else [1.0]) )
    
    if not np.all(np.isfinite(K_demand_sec)): K_demand_sec = np.nan_to_num(K_demand_sec, nan=0, posinf=1e6, neginf=0)
    if not np.all(np.isfinite(L_demand_sec)): L_demand_sec = np.nan_to_num(L_demand_sec, nan=0, posinf=1e6, neginf=0)
    
    K_demand_sec = np.maximum(0, K_demand_sec)
    L_demand_sec = np.maximum(0, L_demand_sec)

    return np.sum(K_demand_sec), np.sum(L_demand_sec), K_demand_sec, L_demand_sec, Y_i_prod_v

def solve_ss_ge_corrected(model_params, a_grid_cfg, income_shock_cfg, ge_tol=1E-4, ge_max_iter=50, r_init_override=None, verbose=False):
    """Solves for the full general equilibrium steady state."""
    # Unpack parameters
    beta = model_params['beta']; theta = model_params['theta']; delta = model_params['delta']
    eps_g = model_params['eps_goods']; omg_g = model_params['omega_goods']
    sig_agg = model_params['sigma_agg']
    N_Sectors = len(eps_g)
    
    # Setup grids
    a_grid = discretize_assets(a_grid_cfg['amin'], a_grid_cfg['amax'], a_grid_cfg['n_a'])
    z_grid, pi_z, Pi_z = discretize_income(income_shock_cfg['rho_z'], income_shock_cfg['sigma_z'], income_shock_cfg['n_z'])
    n_z, n_a = income_shock_cfg['n_z'], a_grid_cfg['n_a']
    
    current_model_params = copy.deepcopy(model_params)
    
    # Set reasonable bounds for the interest rate
    r_min_default, r_max_default = -delta + 1e-4, (1/beta - 1 - 1e-4 if beta < 1-1e-5 else 0.2)
    r_min = current_model_params.get('r_ss_min', r_min_default)
    r_max = current_model_params.get('r_ss_max', r_max_default)

    if r_min >= r_max :
        if verbose: print(f"Warning: r_min {r_min:.4f} >= r_max {r_max:.4f}. Resetting r bounds.")
        r_min, r_max = r_min_default, r_max_default
    if verbose: print(f"Starting GE SS search: r in [{r_min:.4f}, {r_max:.4f}]")

    # Bisection search for the equilibrium interest rate
    for it_ge in range(ge_max_iter):
        r_guess = (r_min + r_max) / 2
        if r_init_override is not None and it_ge == 0: r_guess = r_init_override

        if verbose: print(f"  SS Iter {it_ge+1}/{ge_max_iter}: r_guess = {r_guess:.6f}")

        try:
            w_eq, p_gds_eq, kl_ratios_eq = get_static_prices_and_kl_ratios(r_guess, current_model_params)
        except ValueError as e:
            if verbose: print(f"    Price calc failed for r={r_guess:.5f}: {e}. Adjusting r bounds.")
            r_min = max(r_min, r_guess + 1e-5) # Push r_min up if price calc fails
            if r_min >= r_max - 1e-7:
                 if verbose: print(f"    SS GE bounds invalid after price error. Aborting."); return None
            continue 
        
        # Given prices, solve for household policies
        Lambda_eq, a_pol_eq, C_pol_eq, e_pol_eq = policy_ss_ge_corrected(
            Pi_z, a_grid, z_grid, w_eq, r_guess, p_gds_eq, eps_g, omg_g, sig_agg, 
            beta, theta, current_model_params, tol=1E-7, max_iter=10000 
        )
        
        # Find stationary distribution
        D_ss_eq = distribution_ss(Pi_z, a_pol_eq, a_grid, pi_z, tol=1E-9)
        if not np.isclose(np.sum(D_ss_eq), 1.0): D_ss_eq = D_ss_eq / np.sum(D_ss_eq) 
        
        # Aggregate supply and demand
        A_supply_eq = np.sum(a_pol_eq * D_ss_eq) 

        c_i_pol_eq = np.zeros((n_z, n_a, N_Sectors))
        for zi,ai_idx in np.ndindex(n_z,n_a):
            c_i_pol_eq[zi,ai_idx,:] = get_good_consumptions(
                C_pol_eq[zi,ai_idx],e_pol_eq[zi,ai_idx],p_gds_eq,eps_g,omg_g,sig_agg
            )
        C_agg_g_eq = np.sum(c_i_pol_eq * D_ss_eq[:,:,None], axis=(0,1)) 

        firm_params_ss = current_model_params.copy()
        firm_params_ss['K_next_for_firms'] = A_supply_eq 

        K_demand_eq, L_demand_eq, K_sec_eq, L_sec_eq, Y_i_prod_eq = get_firm_factor_demands_and_prod(
            C_agg_g_eq, A_supply_eq, p_gds_eq, delta, kl_ratios_eq, firm_params_ss
        )
        
        capital_excess_demand = K_demand_eq - A_supply_eq
        
        if verbose: 
            print(f"    K_supply={A_supply_eq:.3f}, K_demand={K_demand_eq:.3f}, Excess K_D={capital_excess_demand:.4f}")

        # Check for convergence and update r bounds
        if abs(capital_excess_demand) < ge_tol :
            if verbose: print(f"  GE converged for K market at r = {r_guess:.5f}")
            results = {
                'r_eq':r_guess, 'w_eq':w_eq, 'p_gds_eq':p_gds_eq, 'K_eq':A_supply_eq, 
                'L_demand_eq':L_demand_eq,'K_sec_eq':K_sec_eq, 'L_sec_eq':L_sec_eq, 
                'Y_i_prod_eq':Y_i_prod_eq, 'Lambda_eq': Lambda_eq,'C_agg_g_eq':C_agg_g_eq, 
                'kl_ratios_eq': kl_ratios_eq,'a_grid':a_grid, 'z_grid':z_grid, 
                'Pi_z':Pi_z, 'pi_z':pi_z, 'D_ss_eq':D_ss_eq,'a_pol_eq':a_pol_eq, 
                'C_pol_eq':C_pol_eq, 'e_pol_eq':e_pol_eq, 'c_i_pol_eq':c_i_pol_eq,
                'model_params':current_model_params, 
                'income_shock_cfg':income_shock_cfg, 'a_grid_cfg':a_grid_cfg
            }
            return results
        
        if capital_excess_demand > 0: r_min = r_guess
        else: r_max = r_guess

        if (r_max - r_min) < 1E-9: 
            if verbose: print(f"  GE r bounds too close. No SS convergence.");
            break
            
    if verbose: print(f"GE SS solver did not converge after {ge_max_iter} iterations.");
    return None

# --- Path finding functions ---
def get_prices_path(r_path, model_params_path, T, ss0_prices=None, ssT_prices=None, verbose=False):
    """Calculates price paths (w, p_i) given a path for r."""
    w_path = np.zeros(T)
    p_gds_path = np.zeros((T, len(model_params_path[0]['A_goods'])))
    kl_ratios_path = np.zeros((T, len(model_params_path[0]['A_goods'])))
    
    for t in range(T):
        try:
            w_path[t], p_gds_path[t,:], kl_ratios_path[t,:] = get_static_prices_and_kl_ratios(r_path[t], model_params_path[t])
        except ValueError as e:
            error_msg = f"Price calculation error at t={t}, r={r_path[t]:.4f}: {e}"
            if t > 0: # Fallback to t-1 prices
                 if verbose: print(f"Warning: {error_msg}. Using t-1 prices as fallback for t={t}.")
                 w_path[t], p_gds_path[t,:], kl_ratios_path[t,:] = w_path[t-1], p_gds_path[t-1,:], kl_ratios_path[t-1,:]
            elif ss0_prices: # Fallback to initial SS prices for t=0
                 if verbose: print(f"Warning: {error_msg}. Using SS0 prices as fallback for t=0.")
                 w_path[t], p_gds_path[t,:], kl_ratios_path[t,:] = ss0_prices['w'], ss0_prices['p'], ss0_prices['kl']
            else:
                raise ValueError(error_msg + " - Cannot proceed without valid prices at t=0.")
                
    return w_path, p_gds_path, kl_ratios_path

def solve_transition_path_ar1_shock(ss0_res, T_trans,
                                    initial_A_shock_frac, rho_A_shock,
                                    ge_tol_trans=1E-3, max_iter_trans=250,
                                    initial_eta_r_step=0.01,
                                    xi_r_update_fixed=0.005,
                                    max_r_change_iter=0.0005,
                                    verbose=True, debug_plots=False):
    """
    CORRECTED: Solves for the transition path after a temporary AR(1) shock to productivity.
    This version fixes the NameError by initializing eta_r_step and restores the
    adaptive learning rate for more robust convergence.
    """
    if verbose: print(f"--- Starting AR(1) Transition Path (T={T_trans}, shock_init={initial_A_shock_frac:.2f}, rho_A={rho_A_shock:.2f}) ---")
    
    # --- 1. Unpack parameters and set up terminal conditions ---
    model_params_ss0 = ss0_res['model_params']
    beta, theta, delta = model_params_ss0['beta'], model_params_ss0['theta'], model_params_ss0['delta']
    eps_g, omg_g, sig_agg = model_params_ss0['eps_goods'], model_params_ss0['omega_goods'], model_params_ss0['sigma_agg']
    num_sectors = len(eps_g)
    
    a_grid, z_grid, Pi_z = ss0_res['a_grid'], ss0_res['z_grid'], ss0_res['Pi_z']
    n_z, n_a = Pi_z.shape[0], len(a_grid)
    
    # Initial steady state (t<0)
    r_ss0, K_ss0, D_ss0 = ss0_res['r_eq'], ss0_res['K_eq'], ss0_res['D_ss_eq']
    w_ss0, p_gds_ss0, kl_ss0 = ss0_res['w_eq'], ss0_res['p_gds_eq'], ss0_res['kl_ratios_eq']
    ss0_prices_struct = {'r': r_ss0, 'w': w_ss0, 'p': p_gds_ss0, 'kl': kl_ss0}

    # Final steady state (t->inf). For a temporary shock, ssT is the same as ss0.
    ssT_res = ss0_res 
    r_ssT, Lambda_ssT = ssT_res['r_eq'], ssT_res['Lambda_eq']
    w_ssT, p_gds_ssT, kl_ssT = ssT_res['w_eq'], ssT_res['p_gds_eq'], ssT_res['kl_ratios_eq']
    ssT_prices_struct = {'r': r_ssT, 'w': w_ssT, 'p': p_gds_ssT, 'kl': kl_ssT}
    
    # --- 2. Define exogenous shock path and initialize guesses ---
    A_goods_ss0_val = model_params_ss0['A_goods']
    if isinstance(initial_A_shock_frac, (int, float)):
        initial_A_shock_frac_arr = np.full(num_sectors, initial_A_shock_frac)
    else:
        initial_A_shock_frac_arr = np.array(initial_A_shock_frac)

    A_goods_path_vals = np.array([A_goods_ss0_val * (1 + initial_A_shock_frac_arr * (rho_A_shock**t)) for t in range(T_trans)])
    model_params_path = [copy.deepcopy(ss0_res['model_params']) for _ in range(T_trans)]
    for t in range(T_trans): model_params_path[t]['A_goods'] = A_goods_path_vals[t,:]

    # Initial guess for the interest rate path.
    r_path = np.full(T_trans, r_ss0)
    
    # --- 3. Main iteration loop to find equilibrium price path ---
    history_max_error = []
    
    # **FIX**: Initialize eta_r_step and other variables for the adaptive update rule.
    eta_r_step = initial_eta_r_step
    last_max_abs_excess_K = float('inf')
    consecutive_error_increases = 0
    stagnation_counter = 0

    for iter_trans in range(max_iter_trans):
        if verbose: print(f"\nAR(1) Transition Iter {iter_trans + 1}/{max_iter_trans}, eta_r_step={eta_r_step:.3e}")
        r_path_old_iter_main = r_path.copy() 

        # A. Given r_path, find all other prices
        try:
            w_path, p_gds_path, kl_ratios_path = get_prices_path(r_path, model_params_path, T_trans, 
                                                                 ss0_prices_struct, ssT_prices_struct, verbose=verbose)
        except ValueError as e: 
            print(f"CRITICAL ERROR in get_prices_path: {e}. Aborting transition."); return None

        # B. Solve household problem backwards in time
        Lambda_path = [np.zeros((n_z,n_a)) for _ in range(T_trans)]
        a_pol_path, C_pol_path, e_pol_path = ([p.copy() for p in Lambda_path] for _ in range(3))
        
        Lambda_next_t = Lambda_ssT 
        for t in range(T_trans - 1, -1, -1):
            mp_hh_t = model_params_path[t] 
            Lambda_path[t], a_pol_path[t], C_pol_path[t], e_pol_path[t] = \
                backward_iteration_ge_corrected(Lambda_next_t, Pi_z, a_grid, z_grid, 
                                                w_path[t], r_path[t], p_gds_path[t,:], 
                                                eps_g, omg_g, sig_agg, beta, theta, mp_hh_t)
            Lambda_next_t = Lambda_path[t]
        
        # C. Simulate the distribution and aggregates forward in time
        D_path = [np.zeros((n_z, n_a)) for _ in range(T_trans + 1)]; D_path[0] = D_ss0.copy()
        K_supply_path, K_hh_savings_path = np.zeros(T_trans), np.zeros(T_trans)
        K_demand_path, L_demand_path = np.zeros(T_trans), np.zeros(T_trans)

        for t in range(T_trans):
            if t == 0: K_supply_path[t] = K_ss0 
            else: K_supply_path[t] = K_hh_savings_path[t-1]
            
            K_hh_savings_path[t] = np.sum(a_pol_path[t] * D_path[t])
            
            c_i_pol_t = np.array([[get_good_consumptions(C_pol_path[t][zi,ai], e_pol_path[t][zi,ai], p_gds_path[t,:], eps_g, omg_g, sig_agg) for ai in range(n_a)] for zi in range(n_z)])
            C_agg_g_t = np.sum(c_i_pol_t * D_path[t][:,:,None], axis=(0,1))

            firm_params_t = model_params_path[t].copy()
            firm_params_t['K_next_for_firms'] = K_hh_savings_path[t] 
            K_demand_path[t], L_demand_path[t], _, _, _ = \
                get_firm_factor_demands_and_prod(C_agg_g_t, K_supply_path[t], p_gds_path[t,:], delta, kl_ratios_path[t,:], firm_params_t)
            
            if t + 1 < len(D_path): D_path[t+1] = forward_iteration(D_path[t], Pi_z, a_pol_path[t], a_grid, n_z, n_a)

        # D. Calculate excess demand and check for convergence
        Excess_K_demand_path = K_demand_path - K_supply_path
        
        Excess_K_update_signal = Excess_K_demand_path.copy()
        Excess_K_update_signal[T_trans-1] = 0.0

        max_abs_excess_K = np.max(np.abs(Excess_K_update_signal))
        history_max_error.append(max_abs_excess_K)
        if verbose: print(f"  Max|Excess K|:{max_abs_excess_K:.4e} (Tol: {ge_tol_trans:.1e})")

        if max_abs_excess_K < ge_tol_trans:
            if verbose: print(f"AR(1) Transition path converged in {iter_trans + 1} iterations.")
            # Final calculation of aggregates for the converged path
            K_sec_path, L_sec_path, Y_i_prod_path = np.zeros((3, T_trans, num_sectors))
            C_agg_g_path = np.zeros((T_trans, num_sectors))
            for t in range(T_trans):
                c_i_pol_t = np.array([[get_good_consumptions(C_pol_path[t][zi,ai], e_pol_path[t][zi,ai], p_gds_path[t,:], eps_g, omg_g, sig_agg) for ai in range(n_a)] for zi in range(n_z)])
                C_agg_g_path[t,:] = np.sum(c_i_pol_t * D_path[t][:,:,None], axis=(0,1))
                firm_params_t = model_params_path[t].copy()
                firm_params_t['K_next_for_firms'] = K_hh_savings_path[t]
                _, _, K_sec_path[t,:], L_sec_path[t,:], Y_i_prod_path[t,:] = \
                    get_firm_factor_demands_and_prod(C_agg_g_path[t,:], K_supply_path[t], p_gds_path[t,:], delta, kl_ratios_path[t,:], firm_params_t)

            results_trans = {'r_path': r_path, 'w_path': w_path, 'p_gds_path': p_gds_path, 'K_supply_path': K_supply_path, 
                             'K_demand_path': K_demand_path, 'Excess_K_demand_path_raw': Excess_K_demand_path, 'K_hh_savings_path': K_hh_savings_path, 
                             'L_demand_path': L_demand_path, 'C_agg_g_path': C_agg_g_path, 'Y_i_prod_path': Y_i_prod_path, 'D_path': D_path, 
                             'kl_ratios_path': kl_ratios_path, 'Lambda_path': Lambda_path, 'a_pol_path':a_pol_path, 'C_pol_path':C_pol_path, 
                             'e_pol_path':e_pol_path, 'K_sec_path': K_sec_path, 'L_sec_path': L_sec_path, 'model_params_path': model_params_path, 
                             'A_goods_path_vals': A_goods_path_vals, 'T_trans': T_trans, 'ss0_res': ss0_res, 'iterations': iter_trans + 1, 
                             'history_max_error': history_max_error, 'initial_A_shock_frac': initial_A_shock_frac_arr, 'rho_A_shock': rho_A_shock}
            return results_trans

        # E. Update the guess for the interest rate path
        proposed_r_increment = xi_r_update_fixed * Excess_K_update_signal
        proposed_r_increment = np.clip(proposed_r_increment, -max_r_change_iter, max_r_change_iter)
        r_path = r_path_old_iter_main + eta_r_step * proposed_r_increment

        for t_idx in range(T_trans): 
            r_min_bound = -model_params_path[t_idx]['delta'] + 1e-4
            r_max_bound = (1/model_params_path[t_idx]['beta'] - 1 - 1e-4) if model_params_path[t_idx]['beta'] < 1-1e-5 else 0.2
            r_path[t_idx] = np.clip(r_path[t_idx], r_min_bound, r_max_bound)

        # **FIX**: Restore adaptive learning rate for eta_r_step
        if max_abs_excess_K > last_max_abs_excess_K * 1.05 and iter_trans > 5 : 
            consecutive_error_increases +=1
            stagnation_counter = 0
            if consecutive_error_increases >= 2: 
                eta_r_step = max(eta_r_step * 0.5, initial_eta_r_step * 0.001) 
                if verbose: print(f"    Error increased. Eta reduced: eta={eta_r_step:.3e}")
                consecutive_error_increases = 0
        elif max_abs_excess_K < last_max_abs_excess_K * 0.98 : 
            eta_r_step = min(eta_r_step * 1.02, initial_eta_r_step * 2.0) 
            consecutive_error_increases = 0
            stagnation_counter = 0
        else: 
            stagnation_counter +=1
            if stagnation_counter >= 8 and iter_trans > 15: 
                eta_r_step = max(eta_r_step * 0.7, initial_eta_r_step * 0.001)
                if verbose: print(f"    Error stagnant. Eta reduced: eta={eta_r_step:.3e}")
                stagnation_counter = 0
        
        last_max_abs_excess_K = max_abs_excess_K

    if verbose: print(f"AR(1) Transition DID NOT converge. Max|EKD|:{max_abs_excess_K:.4e}")
    return None

# --- Plotting Function for AR(1) shock (log-differences from SS0) ---
def plot_transition_paths_log_diff(trans_res, shock_time=0, verbose_plot=False):
    """Plots transition paths as log-deviations from the initial steady state."""
    if not trans_res or 'ss0_res' not in trans_res or 'K_supply_path' not in trans_res:
        if verbose_plot: print("No valid/complete transition results to plot.")
        return

    T = trans_res['T_trans']; time_grid = np.arange(T)
    ss0 = trans_res['ss0_res']
    delta = ss0['model_params']['delta']
    num_sectors_total = len(ss0['model_params']['A_goods'])
    
    # Unpack SS and path variables
    K_ss0, r_ss0, w_ss0, p_gds_ss0, C_agg_g_ss0, Y_i_prod_ss0, L_demand_ss0, K_sec_ss0, L_sec_ss0 = (
        ss0['K_eq'], ss0['r_eq'], ss0['w_eq'], ss0['p_gds_eq'], ss0['C_agg_g_eq'], 
        ss0['Y_i_prod_eq'], ss0['L_demand_eq'], ss0['K_sec_eq'], ss0['L_sec_eq']
    )
    K_path, K_plus1_path, r_path, w_path, L_demand_path, p_gds_path, C_agg_g_path, Y_i_prod_path, K_sec_path, L_sec_path = (
        trans_res['K_supply_path'], trans_res['K_hh_savings_path'], trans_res['r_path'], 
        trans_res['w_path'], trans_res['L_demand_path'], trans_res['p_gds_path'], 
        trans_res['C_agg_g_path'], trans_res['Y_i_prod_path'], trans_res.get('K_sec_path'), trans_res.get('L_sec_path')
    )

    def plot_single_log_diff(ax, data_path, ss0_val, title, label_base, is_rate=False, is_inv_rate=False, K_path_for_inv_rate=None):
        if is_rate: # Plot level deviation for rates
            plot_data = data_path - ss0_val
            ylabel = f'{label_base} (dev from SS0)'
        elif is_inv_rate: # Plot investment rate I/K
            inv_rate_path = data_path / np.maximum(K_path_for_inv_rate, 1e-9)
            inv_rate_ss0 = (ss0_val * K_ss0) / K_ss0 # I_ss0/K_ss0 = delta
            plot_data = inv_rate_path - inv_rate_ss0
            ylabel = f'{label_base} rate (dev from SS0 rate)'
        else: # Plot log deviation
            safe_ss0_val = np.maximum(ss0_val, 1e-9)
            plot_data = np.log(np.maximum(data_path, 1e-12) / safe_ss0_val)
            ylabel = f'{label_base} (log-diff from SS0)'
        
        ax.plot(time_grid, plot_data, lw=2)
        ax.axhline(0, color='black', linestyle=':')
        ax.set_title(title); ax.set_xlabel('Time'); ax.set_ylabel(ylabel)
        if shock_time >= 0 and shock_time < T : ax.axvline(shock_time, color='gray', linestyle='--', lw=1.5, label='Shock')
        ax.legend([label_base, 'SS0 level', 'Shock'], loc='best')

    # --- Aggregate Plots ---
    fig_agg, axes_agg = plt.subplots(4, 2, figsize=(15, 16))
    axes_agg = axes_agg.flatten()
    plot_single_log_diff(axes_agg[0], K_path, K_ss0, 'Agg. Capital Stock', 'K_t')
    plot_single_log_diff(axes_agg[1], (K_plus1_path - (1-delta)*K_path), delta, 'Gross Investment Rate (I/K)', 'I_t/K_t', is_inv_rate=True, K_path_for_inv_rate=K_path)
    plot_single_log_diff(axes_agg[2], r_path, r_ss0, 'Interest Rate', 'r_t', is_rate=True)
    plot_single_log_diff(axes_agg[3], w_path, w_ss0, 'Wage Rate', 'w_t')
    plot_single_log_diff(axes_agg[4], L_demand_path, L_demand_ss0, 'Agg. Labor Demand', 'L_d,t')
    
    total_C_value_path = np.sum(p_gds_path * C_agg_g_path, axis=1)
    total_C_value_ss0 = np.sum(p_gds_ss0 * C_agg_g_ss0)
    plot_single_log_diff(axes_agg[5], total_C_value_path, total_C_value_ss0, 'Total Consumption Value', 'C_val_t')
    
    total_Y_value_path = np.sum(p_gds_path * Y_i_prod_path, axis=1)
    total_Y_value_ss0 = np.sum(p_gds_ss0 * Y_i_prod_ss0)
    plot_single_log_diff(axes_agg[6], total_Y_value_path, total_Y_value_ss0, 'Total Output Value', 'Y_val_t')
    
    total_C_physical_path = np.sum(C_agg_g_path, axis=1)
    total_C_physical_ss0 = np.sum(C_agg_g_ss0)
    plot_single_log_diff(axes_agg[7], total_C_physical_path, total_C_physical_ss0, 'Total Physical Consumption (Sum C_i)', 'C_phys_t')
    
    fig_agg.tight_layout(pad=2.0); fig_agg.suptitle("Aggregate Log-Difference Transition Paths (vs SS0)", fontsize=16, y=1.02); plt.show()

    # --- Sectoral Plots (Subset of Sectors) ---
    sectors_to_plot_idx = sorted(list(set([0, num_sectors_total // 2, num_sectors_total - 1])))
    num_sel_sectors = len(sectors_to_plot_idx)
    
    # Sectoral Prices (excluding numeraire sector 0)
    if num_sectors_total > 1:
        price_indices_to_plot = [idx for idx in sectors_to_plot_idx if idx > 0]
        if not price_indices_to_plot and num_sectors_total > 1: price_indices_to_plot = [1]
        
        fig_p, axes_p = plt.subplots(1, len(price_indices_to_plot), figsize=(len(price_indices_to_plot) * 5, 4), sharey=True, squeeze=False)
        for col_idx, sec_idx in enumerate(price_indices_to_plot):
            plot_single_log_diff(axes_p[0,col_idx], p_gds_path[:,sec_idx], p_gds_ss0[sec_idx], f'Price Sector {sec_idx}', f'p_s{sec_idx}')
        fig_p.tight_layout(pad=2.0); fig_p.suptitle(f"Selected Sectoral Price Paths", fontsize=16, y=1.03); plt.show()

    # Sectoral C & Y
    fig_cy, axes_cy = plt.subplots(2, num_sel_sectors, figsize=(num_sel_sectors * 5, 8), sharex=True, squeeze=False)
    for col_idx, sec_idx in enumerate(sectors_to_plot_idx):
        plot_single_log_diff(axes_cy[0,col_idx], C_agg_g_path[:,sec_idx], C_agg_g_ss0[sec_idx], f'Cons. Sector {sec_idx}', f'C_s{sec_idx}')
        plot_single_log_diff(axes_cy[1,col_idx], Y_i_prod_path[:,sec_idx], Y_i_prod_ss0[sec_idx], f'Prod. Sector {sec_idx}', f'Y_s{sec_idx}')
    fig_cy.tight_layout(pad=2.0); fig_cy.suptitle("Selected Sectoral C & Y Paths", fontsize=16, y=1.02); plt.show()

    # Sectoral K & L
    if K_sec_path is not None and L_sec_path is not None:
        fig_kl, axes_kl = plt.subplots(2, num_sel_sectors, figsize=(num_sel_sectors * 5, 8), sharex=True, squeeze=False)
        for col_idx, sec_idx in enumerate(sectors_to_plot_idx):
            plot_single_log_diff(axes_kl[0,col_idx], K_sec_path[:,sec_idx], K_sec_ss0[sec_idx], f'Capital Sector {sec_idx}', f'K_s{sec_idx}')
            plot_single_log_diff(axes_kl[1,col_idx], L_sec_path[:,sec_idx], L_sec_ss0[sec_idx], f'Labor Sector {sec_idx}', f'L_s{sec_idx}')
        fig_kl.tight_layout(pad=2.0); fig_kl.suptitle("Selected Sectoral K & L Paths", fontsize=16, y=1.02); plt.show()

# --- Plotting functions (for SS policy, adapted for N_SECTORS) ---
def plot_all_policy_functions(res, verbose_plot=False):
    """Plots steady-state policy functions and distributions."""
    if not res:
        if verbose_plot: print("No results to plot policy functions.")
        return

    a_g, z_g = res['a_grid'], res['z_grid']
    n_z = res['income_shock_cfg']['n_z']
    N_Sectors = len(res['model_params']['A_goods'])
    z_idx_plot = sorted(list(set([0, n_z // 2, n_z - 1]))) if n_z > 3 else list(range(n_z))
    z_lbls = [f'z={z_g[i]:.2f}' for i in z_idx_plot]

    fig, axs = plt.subplots(2, 2, figsize=(14, 10))
    axs[0,0].plot(a_g, a_g, 'k--', label="45-deg")
    for i,zi in enumerate(z_idx_plot): axs[0,0].plot(a_g, res['a_pol_eq'][zi,:], label=z_lbls[i])
    axs[0,0].set_title("Asset Policy Function $a'(a,z)$"); axs[0,0].set_xlabel('a'); axs[0,0].legend(); axs[0,0].grid(True)
    
    for i,zi in enumerate(z_idx_plot): axs[0,1].plot(a_g, res['C_pol_eq'][zi,:], label=z_lbls[i])
    axs[0,1].set_title("Consumption Policy Function $C(a,z)$"); axs[0,1].set_xlabel('a'); axs[0,1].legend(); axs[0,1].grid(True)

    for i,zi in enumerate(z_idx_plot): axs[1,0].plot(a_g, res['e_pol_eq'][zi,:], label=z_lbls[i])
    axs[1,0].set_title("Expenditure Policy Function $e(a,z)$"); axs[1,0].set_xlabel('a'); axs[1,0].legend(); axs[1,0].grid(True)
    
    D_ss_marginal_a = np.sum(res['D_ss_eq'], axis=0)
    axs[1,1].plot(res['a_grid'], D_ss_marginal_a)
    axs[1,1].set_title('Stationary Asset Distribution (Marginal over z)'); axs[1,1].set_xlabel('a'); axs[1,1].grid(True)
    
    fig.tight_layout(); plt.show()
    
#%%
# --- Main execution ---
if __name__ == '__main__':
    master_verbose = True
    plot_verbose = True

    N_SECTORS = 3
    print(f"--- Configuring for {N_SECTORS} Sectors ---")
    
    base_params = {
        'beta': 1-0.08/4, 'theta': 2, 'delta': 0.025, 'sigma_agg': 0.5,
        'eps_goods': np.array([0.05, 1, 1.2]),
        'omega_goods': np.ones(N_SECTORS),
        'A_goods': np.ones(N_SECTORS),
        'alpha_goods': np.linspace(0.55, 0.75, N_SECTORS),
    }
    a_cfg_main = {'amin':0.0, 'amax':150, 'n_a':100}
    inc_cfg_main = {'rho_z':0.96, 'sigma_z':0.4, 'n_z':5}
    
    print("--- Solving for Initial Steady State (SS0) ---")
    ss0_results = solve_ss_ge_corrected(base_params, a_cfg_main, inc_cfg_main, 
                                        ge_tol=1E-3, ge_max_iter=70, verbose=master_verbose)
                                        
    if not ss0_results:
        print("Failed to solve for SS0. Aborting.")
    else:
        print("\n--- SS0 Results ---")
        print(f"r_ss0: {ss0_results['r_eq']:.4f}, K_ss0: {ss0_results['K_eq']:.3f}, w_ss0: {ss0_results['w_eq']:.3f}")
        if master_verbose: plot_all_policy_functions(ss0_results, verbose_plot=plot_verbose)

        # --- Transition Path Simulation ---
        initial_A_shock_percentage = -0.05
        rho_A_shock_persistence = 0.80    
        T_transition_periods = 150       

        print(f"\n--- Solving for Transition Path with AR(1) shock to A_goods ---")
        transition_results_ar1 = solve_transition_path_ar1_shock(
            ss0_res=ss0_results,
            T_trans=T_transition_periods,
            initial_A_shock_frac=initial_A_shock_percentage,
            rho_A_shock=rho_A_shock_persistence,
            ge_tol_trans=1e-5,       
            max_iter_trans=200,      
            initial_eta_r_step=0.05,  
            xi_r_update_fixed=0.01,  
            max_r_change_iter=0.005,  
            verbose=master_verbose
        )
        
        if transition_results_ar1:
            print(f"\n--- AR(1) Transition Path Solved in {transition_results_ar1.get('iterations', 'N/A')} iterations ---")
            plot_transition_paths_log_diff(transition_results_ar1, shock_time=0, verbose_plot=plot_verbose)
        else:
            print("\n--- AR(1) Transition Path Failed to Converge ---")