{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tutorial 3: A one-asset HANK model\n", "\n", "In this notebook we solve the one-asset HANK model from <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> (2021): \"Using the Sequence-Space Jacobian to Solve and Estimate Heterogeneous-Agent Models\" ([link to paper](https://www.bencebardoczy.com/publication/sequence-jacobian/sequence-jacobian.pdf)).\n", "\n", "New concepts:\n", "- **Hetinputs and hetoutputs**: adapt off-the-shelf HA blocks to new macro models.\n", "- **Calibration DAG**: exploit analytical part of internal calibration \n", "\n", "For more examples and information on the SSJ toolkit, please visit our [GitHub page](https://github.com/shade-econ/sequence-jacobian)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "from sequence_jacobian import simple, create_model  # functions\n", "from sequence_jacobian import hetblocks, grids      # modules"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1 Model description\n", "\n", "The model is a HA version of the textbook New Keynesian model. Technology is linear in labor, prices are sticky á la Rotemberg, and monetary policy follows a Taylor rule. The model can be summarized in sequence form as\n", "\n", "$$\n", "\\textbf{F}_t(\\textbf{X}, Z) \\equiv \n", "\\begin{pmatrix}\n", "Y_t - Z_t L_t\n", "\\\\\n", "Y_t \\left[1 - \\frac{\\mu}{\\mu-1}\\frac{1}{2\\kappa} \\log(1 + \\pi_t)^2\\right] - w_t L_t - d_t\n", "\\\\\n", "r_t B - \\tau_t\n", "\\\\\n", "r^*_t + \\phi \\pi_t - i_t\n", "\\\\\n", "1 + r_t - \\frac{1+i_{t-1}}{1+\\pi_t}\n", "\\\\\n", "\\kappa \\left(\\frac{w_t}{Z_t} - \\frac{1}{\\mu} \\right)  + \\frac{1}{1+r_{t+1}} \\frac{Y_{t+1}}{Y_t} \\log(1+\\pi_{t+1}) - \\log(1+\\pi_t)\n", "\\\\\n", "\\mathcal{A}_t(\\{r_s, w_s, \\tau_s, d_s\\})  - B\n", "\\\\\n", "\\mathcal{N}_t(\\{r_s, w_s, \\tau_s, d_s\\}) - L_t\n", "\\end{pmatrix}\n", "= \\begin{pmatrix} 0 \\\\ 0 \\\\ 0 \\\\ 0 \\\\ 0 \\\\ 0 \\\\ 0 \\\\ 0\\end{pmatrix},\n", "\\qquad t = 0, 1, \\dots\n", "$$\n", "\n", "where the endogenous variables are $\\textbf{X} = (Y, L, r, w, d, \\pi, \\tau, i)$ and the exogenous variables are $\\textbf{Z}=(r^*, Z)$. \n", "\n", "The asset demand and labor supply functions $\\{\\mathcal{A}, \\mathcal{L}\\}$ follow from the household block with <PERSON><PERSON> equation\n", "\n", "$$\n", "\\begin{align} \\tag{HH}\n", "V_t(e, a_{-}) = \\max_{c, n, a} &\\left\\{\\frac{c^{1-\\sigma}}{1-\\sigma} - \\varphi \\frac{n^{1+\\nu}}{1+\\nu} + \\beta \\mathbb{E}_t\\left[V_{t+1}(e', a)|e \\right] \\right\\}\n", "\\\\\n", "c + a &= (1 + r_t)a_{-} + w_t e n - \\tau_t \\bar{\\tau}(e) + d_t \\bar{d}(e)\n", "\\\\\n", "a &\\geq 0\n", "\\end{align}\n", "$$\n", "\n", "where $\\bar\\tau(e)$ and $\\bar d(e)$ are skill-specific incidence rules for taxes and dividends. \n", "\n", "We can think of the model as a directed acyclical graph (DAG) with **3 endogenous inputs** and write it as an implicit function\n", "\n", "$$\n", "H(\\pi, Y, w; \\epsilon, Z) = 0.\n", "$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2 Embed HA block\n", "\n", "As we have seen in the <PERSON><PERSON><PERSON><PERSON><PERSON> notebook, the main task in setting up HA blocks is to write a backward iteration function that represents the <PERSON><PERSON> equation. This has to be a single step of an iterative solution method such as value function iteration. For the standard income fluctuation problem with endogenous labor supply we're dealing with here, the endogenous gridpoint method of [<PERSON> (2006)](https://www.sciencedirect.com/science/article/pii/S0165176505003368) is the best practice.\n", "\n", "Solving the endogenous-labor problem via EGM is standard but somewhat tedious and so the details are left to ``sequence_jacobian/hetblocks/hh_labor.py``. Instead we will focus on how to adapt this off-the-shelf HetBlock to our specific macro enviroment."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<HetBlock 'hh'>\n", "Inputs: ['a_grid', 'we', 'T', 'r', 'beta', 'eis', 'frisch', 'vphi', 'Pi']\n", "Macro outputs: ['A', 'C', 'N']\n", "Micro outputs: ['D', 'Dbeg', '<PERSON>', 'Va', 'a', 'c', 'n']\n"]}], "source": ["hh = hetblocks.hh_labor.hh\n", "\n", "print(hh)\n", "print(f'Inputs: {hh.inputs}')\n", "print(f'Macro outputs: {hh.outputs}')\n", "print(f'Micro outputs: {hh.internals}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The HetBlock `hh` corresponds to the general problem:\n", "\n", "$$\n", "\\begin{align} \\tag{HH-general}\n", "V_t(e, a_{-}) = \\max_{c, n, a} &\\left\\{\\frac{c^{1-\\sigma}}{1-\\sigma} - \\varphi \\frac{n^{1+\\nu}}{1+\\nu} + \\beta \\mathbb{E}_t\\left[V_{t+1}(e', a)|e\\right] \\right\\}\n", "\\\\\n", "c + a &= (1 + r_t)a_{-} + w_t(e) n + T_t(e)\n", "\\\\\n", "a &\\geq 0\n", "\\end{align}\n", "$$\n", "\n", "That is, households take as given the sequence of interest rates $r_t$, and skill-specific wages $w_t(e)$ and  transfers $T_t(e).$ In the context of this particular HANK model, transfers equal dividends minus taxes. But it's easy to imagine many other cases. Rather than writing a specific backward iteration function for each of them, we can just supply a function that specifies how the $\\{w_t(e), T_t(e)\\}$ are determined in this particular case. We refer such functions as **hetinput**. \n", "\n", "In addition, we need to report effective labor supply $ne = n\\cdot e$ to resolve labor market clearing. We can do so by attaching a **hetoutput** function to the core HetBlock. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1 <PERSON><PERSON><PERSON>s\n", "\n", "Let's start with the hetinputs. These functions will be evaluated before the core HetBlock (i.e. the backward iteration)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def make_grid(rho_e, sd_e, nE, amin, amax, nA):\n", "    e_grid, pi_e, Pi = grids.markov_rouwenhorst(rho=rho_e, sigma=sd_e, N=nE)\n", "    a_grid = grids.agrid(amin=amin, amax=amax, n=nA)\n", "    return e_grid, pi_e, Pi, a_grid\n", "\n", "\n", "def transfers(pi_e, Div, Tax, e_grid):\n", "    # hardwired incidence rules are proportional to skill; scale does not matter \n", "    tax_rule, div_rule = e_grid, e_grid\n", "    div = Div / np.sum(pi_e * div_rule) * div_rule\n", "    tax = Tax / np.sum(pi_e * tax_rule) * tax_rule\n", "    T = div - tax\n", "    return T\n", "\n", "def wages(w, e_grid):\n", "    we = w * e_grid\n", "    return we"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- The second hetinput `transfers` takes inputs (`e_grid` and `pi_e`) that are produced by the first hetinput `make_grid`. Such *acylic* dependence between hetinputs is allowed. The block processes the inputs and outputs of the hetinput functions and puts them in a correct order of evaluation.\n", "- Scalar-valued inputs of hetinputs may be time-varying. For example, aggregate dividends and taxes (`Div` and `Tax`) are determined endogenously in the HANK model and passed on to <PERSON><PERSON> equation through the hetinput `transfers`. Thus, we can compute Jacobians with respect to them."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's attach these hetinputs to the household block using (the aptly-named) ``HetBlock.add_hetinput`` method.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<HetBlock 'hh' with hetinput 'make_grid_transfers'>\n", "Inputs: ['r', 'beta', 'eis', 'frisch', 'vphi', 'rho_e', 'sd_e', 'nE', 'amin', 'amax', 'nA', 'Div', 'Tax', 'w']\n"]}], "source": ["hh1 = hh.add_hetinputs([make_grid, transfers, wages])\n", "\n", "print(hh1)\n", "print(f'Inputs: {hh1.inputs}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that `hh1` has only scalar-valued inputs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "Hetoutput functions are analogous to hetinputs. They are called after the backward iteration has converged. Thus, they may take multidimensional outputs of the backward iteration function as well as of the hetinputs as inputs."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def labor_supply(n, e_grid):\n", "    ne = e_grid[:, np.newaxis] * n\n", "    return ne"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's attach this hetoutput to the household block using ``HetBlock.add_hetoutput`` method."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<HetBlock 'hh' with hetinput 'make_grid_transfers' and with hetoutput `labor_supply'>\n", "Outputs: ['A', 'C', 'N', 'NE']\n"]}], "source": ["hh_ext = hh1.add_hetoutputs([labor_supply])\n", "\n", "print(hh_ext)\n", "print(f'Outputs: {hh_ext.outputs}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- The signature of <PERSON><PERSON><PERSON><PERSON> `hh_ext` now references both hetinputs and hetoutputs.\n", "- Aggregate outputs now include effective labor supply `NE`. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Taking stock\n", "\n", "The SSJ toolkit comes with 3 generic HetBlocks, located in `sequence_jacobian/hetblocks`.\n", "- `hh_sim`: standard incomplete markets model\n", "- `hh_labor`: standard incomplete markets model with frictionless labor supply\n", "- `hh_twoasset`: two-asset model with convex portfolio adjustment cost\n", "\n", "By using hetinputs and hetoutputs, these core blocks may be embedded in different macro environments. This is the simplest way of using the SSJ toolkit, which may suffice for many applications.\n", "\n", "If you wish to solve a model that's not just a variation on these off-the-shelf HetBlocks, there's two cases to consider. \n", "1. The model fits into the HetBlock paradigm. E.g., standard incomplete markets models with additional choices such as search intensity. All you need to do is write a new backward iteration function. Use it to instantiate a new HetBlock and get all the HetBlock methods for free. \n", "2. The model does not fit the HetBlock paradigm. E.g., models in which discrete endogenous states. In this case, we recommend that you \"bring your own Jacobian\". That is, solve the Jacobian of your block outside the SSJ toolkit. Once you turn them into an instance of `JacobianDict` (like we did in section 4 of the <PERSON><PERSON><PERSON><PERSON> notebook), you can include them in models in lieu of an actual block. This is sufficient for using linear solution methods (`impulse_linear`, `jacobian` and their `solved_` versions) at the macro model level.  \n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 Calibrating the steady state\n", "Similarly to the RBC example, we calibrate the discount factor $\\beta$ and disutility of labor $\\varphi$ to hit a target for the interest rate and effective labor $L=1.$ Additionally we calibrate the wage $w$ such that the Phillips curve relation is satisfied in steady state for zero inflation $\\pi=0$.\n", "\n", "Note that the mapping from $\\beta$ to asset market clearing given $r$ and that from $\\varphi$ to average effective labor supply involve the household block. As such, we must rely on numerical root-finding. In contrast, the $w$ is easy to characterize analytically from the ss-version of NKPC:\n", "$$\n", "w = \\frac{Z}{\\mu} \\tag{ss wage}\n", "$$\n", "This situation is very common in HA-DSGE models. Although using a numerical root-finder on every unknown jointly is likely to succeed in simple models, exploiting analytical solutions becomes crucial in more complicated models.\n", "\n", "The simplest way of doing so is to work with two DAGs: one for calibration and one for transition dynamics. Typically, the two DAGs share some, but not all of their blocks. Furthermore, they may have different unknowns and targets. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 Blocks for SS-DAG\n", "\n", "We already implemented the household block. Let's define the rest as simple blocks."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["@simple\n", "def firm(Y, w, Z, pi, mu, kappa):\n", "    L = Y / Z\n", "    Div = Y - w * L - mu/(mu-1)/(2*kappa) * (1+pi).apply(np.log)**2 * Y\n", "    return L, Div\n", "\n", "\n", "@simple\n", "def monetary(pi, rstar, phi):\n", "    r = (1 + rstar(-1) + phi * pi(-1)) / (1 + pi) - 1\n", "    return r\n", "\n", "\n", "@simple\n", "def fiscal(r, B):\n", "    Tax = r * B\n", "    return Tax\n", "\n", "\n", "@simple\n", "def mkt_clearing(A, NE, C, L, Y, B, pi, mu, kappa):\n", "    asset_mkt = A - B\n", "    labor_mkt = NE - L\n", "    goods_mkt = Y - C - mu/(mu-1)/(2*kappa) * (1+pi).apply(np.log)**2 * Y\n", "    return asset_mkt, labor_mkt, goods_mkt\n", "\n", "\n", "@simple\n", "def nkpc_ss(Z, mu):\n", "    w = Z / mu\n", "    return w"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- These blocks---with the exception of `nkpc_ss`--- are valid in transition as well as in steady state. \n", "- Notice that we wrap `np.log()` in an `.apply()` inside simple blocks. This is necessary for functions whose name includes a dot. The reason is that it would interfere with some internal processing that simple blocks do to handle leads, lags, and references to steady state.  "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Model 'One-Asset HANK SS'>\n", "Inputs: ['beta', 'eis', 'frisch', 'vphi', 'rho_e', 'sd_e', 'nE', 'amin', 'amax', 'nA', 'Y', 'Z', 'pi', 'mu', 'kappa', 'rstar', 'phi', 'B']\n"]}], "source": ["blocks_ss = [hh_ext, firm, monetary, fiscal, mkt_clearing, nkpc_ss]\n", "\n", "hank_ss = create_model(blocks_ss, name=\"One-Asset HANK SS\")\n", "\n", "print(hank_ss)\n", "print(f\"Inputs: {hank_ss.inputs}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Print inputs so we don't have to remember everything."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["calibration = {'eis': 0.5, 'frisch': 0.5, 'rho_e': 0.966, 'sd_e': 0.5, 'nE': 7,\n", "               'amin': 0.0, 'amax': 150, 'nA': 500, 'Y': 1.0, 'Z': 1.0, 'pi': 0.0,\n", "               'mu': 1.2, 'kappa': 0.1, 'rstar': 0.005, 'phi': 1.5, 'B': 5.6}\n", "\n", "unknowns_ss = {'beta': 0.986, 'vphi': 0.8}\n", "targets_ss = {'asset_mkt': 0, 'labor_mkt': 0}\n", "\n", "ss0 = hank_ss.solve_steady_state(calibration, unknowns_ss, targets_ss, solver=\"hybr\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's see the targets and <PERSON><PERSON><PERSON>'s law."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Asset market clearing:  1.42e-14\n", "Labor market clearing:  4.44e-16\n", "Goods market clearing (untargeted): -5.26e-09\n"]}], "source": ["print(f\"Asset market clearing: {ss0['asset_mkt']: 0.2e}\")\n", "print(f\"Labor market clearing: {ss0['labor_mkt']: 0.2e}\")\n", "print(f\"Goods market clearing (untargeted): {ss0['goods_mkt']: 0.2e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looks good. Let's also plot the labor supply policy as function of assets for each skill type. We see that poorer and more productive households choose to work more. "]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.plot(ss0.internals['hh']['a_grid'], ss0.internals['hh']['n'].T)\n", "plt.xlabel('Assets'), plt.ylabel('Labor supply')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4 Linearized dynamics using Jacobians\n", "Recall that we reduced the model to an implicit equation\n", "$$\n", "H(\\pi, Y, w; r^*, Z) =\n", "\\begin{pmatrix}\n", "\\kappa \\left(\\frac{w_t}{Z_t} - \\frac{1}{\\mu} \\right)  + \\frac{1}{1+r_{t+1}} \\frac{Y_{t+1}}{Y_t} \\log(1+\\pi_{t+1}) - \\log(1+\\pi_t)\n", "\\\\\n", "\\mathcal{A}_t(\\{r_s, w_s, \\tau_s, d_s\\})  - B\n", "\\\\\n", "\\mathcal{L}_t(\\{r_s, w_s, \\tau_s, d_s\\}) - L_t\n", "\\end{pmatrix}\n", "= \\begin{pmatrix} 0 \\\\ 0 \\\\ 0\\end{pmatrix},\n", "$$\n", "\n", "to be solved for $U=(\\pi, w, Y)$ given any $Z=(Z, r^*)$. The rest of the endogenous variables are be obtained as explicit functions of $(\\pi, w, Y; Z, r^*)$ along the DAG, but it would be tedious to write them out.\n", "\n", "Keep in mind that the implicit function theorem implies that the response of unknowns is\n", "\n", "$$\n", "dU = \\underbrace{-H_U^{-1}H_Z}_{G_U} dZ \\tag{1}\n", "$$\n", "\n", "Recall that we already solved for a steady state and stored is as `ss`. Furthermore, we will use a 300-period truncation horizon. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.1 Set up the DAG\n", "\n", "Let's set up the second (main) DAG. We just have to replace `nkpc_ss` with a full Phillips curve."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<SimpleBlock 'monetary'>\n", "<SimpleBlock 'nkpc'>\n", "<SimpleBlock 'fiscal'>\n", "<SimpleBlock 'firm'>\n", "<HetBlock 'hh' with hetinput 'make_grid_transfers' and with hetoutput `labor_supply'>\n", "<SimpleBlock 'mkt_clearing'>\n"]}], "source": ["@simple\n", "def nkpc(pi, w, Z, Y, r, mu, kappa):\n", "    nkpc_res = kappa * (w / Z - 1 / mu) + Y(+1) / Y * (1 + pi(+1)).apply(np.log) / (1 + r(+1))\\\n", "               - (1 + pi).apply(np.log)\n", "    return nkpc_res\n", "\n", "\n", "blocks = [hh_ext, firm, monetary, fiscal, mkt_clearing, nkpc]\n", "hank = create_model(blocks, name=\"One-Asset HANK\")\n", "\n", "print(*hank.blocks, sep='\\n')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- Recall that `create_model` performs a topological sort to put the blocks in a (not necessarily unique) correct order of evaluation. Here, `monetary` must be first, then `nkpc`, `fiscal`, `firm` in any order, then `hh`, and finally `mkt_clearing`. Note that `hank.blocks` is a list in such an order."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before we proceed, let's make sure that `ss0` is consistent with the second DAG. Just evaluate the `hank` at `ss0` and verify that the equilibrium conditions hold as expected."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["ss = hank.steady_state(ss0)\n", "\n", "for k in ss0.keys():\n", "    assert np.all(np.isclose(ss[k], ss0[k]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 <PERSON><PERSON><PERSON> <PERSON> Jacobian \n", "\n", "With the model object `hank` in hand, we can get the general equilibrium Jacobians by using its `solve_jacobian` method."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<JacobianDict outputs=['pi', 'w', 'Y', 'A', 'C', 'N', 'NE', 'L', 'Div', 'r', 'Tax', 'asset_mkt', 'labor_mkt', 'goods_mkt', 'nkpc_res'], inputs=['rstar', 'Z']>\n"]}], "source": ["# setup\n", "T = 300\n", "exogenous = ['rstar', 'Z']\n", "unknowns = ['pi', 'w', 'Y']\n", "targets = ['nkpc_res', 'asset_mkt', 'labor_mkt']\n", "\n", "# general equilibrium jacobians\n", "G = hank.solve_jacobian(ss, unknowns, targets, exogenous, T=T)\n", "\n", "print(G)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Under the hood, the `solve_jacobian` method performs the following steps:\n", " - computes the partial Jacobians $\\mathcal{J}^{o,i}$ for all blocks (if their Jacobian is not supplied already), only with respect to the inputs that actually change: unknowns, exogenous shocks, outputs of earlier blocks\n", " - forward accumulates partial Jacobians $\\mathcal{J}^{o,i}$ to form total Jacobians $\\mathbf{J}^{o,i}$\n", " - packs $\\mathbf{J}^{o,i}$ to form $\\mathbf{H_U}$ and $\\mathbf{H_Z}$\n", " - solves for the GE Jacobians for unknowns $\\mathbf{G_U} = \\mathbf{H_U}^{-1}\\mathbf{H_Z}$\n", " - forward accumulates GE Jacobians to obtain $\\mathbf{G}$ for other endogenous variables "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Results\n", "\n", "Now let's consider 25 basis point monetary policy shocks with different persistences and plot the response of inflation."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["rhos = np.array([0.2, 0.4, 0.6, 0.8, 0.9])\n", "\n", "drstar = -0.0025 * rhos ** (np.arange(T)[:, np.newaxis])\n", "dpi = G['pi']['rstar'] @ drstar\n", "\n", "plt.plot(10000 * dpi[:21])\n", "plt.title(r'Inflation responses monetary policy shocks')\n", "plt.xlabel('quarters')\n", "plt.ylabel('bp deviation from ss')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Nonlinear dynamics\n", "Conceptually there's nothing new in this section compared to the <PERSON><PERSON><PERSON><PERSON><PERSON> notebook. We're going to implement a quasi-Newton algorithm to solve for the unknown sequences $U=(\\pi, w, Y)$ given some sequences of shocks $(r^*, Z).$ We initialize the algorithm by the naive guess that the variables in $U$ stay constant at their steady-state level. Then we evaluate the DAG and update the guess using the inverse Jacobian $H_U^{-1}.$ The algorithm converges in a few steps, despite the presence of substantial nonlinearities."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.1 A typical monetary policy shock\n", "Note that the linearized solution ignores price adjustment costs. For a monetary policy shock of typical size and persistence, this does not really matter.   "]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solving One-<PERSON><PERSON> HANK for ['pi', 'w', 'Y'] to hit ['nkpc_res', 'asset_mkt', 'labor_mkt']\n", "On iteration 0\n", "   max error for nkpc_res is 0.00E+00\n", "   max error for asset_mkt is 1.46E-02\n", "   max error for labor_mkt is 2.73E-03\n", "On iteration 1\n", "   max error for nkpc_res is 1.16E-06\n", "   max error for asset_mkt is 1.33E-04\n", "   max error for labor_mkt is 6.89E-06\n", "On iteration 2\n", "   max error for nkpc_res is 4.81E-08\n", "   max error for asset_mkt is 2.21E-06\n", "   max error for labor_mkt is 1.42E-07\n", "On iteration 3\n", "   max error for nkpc_res is 1.26E-09\n", "   max error for asset_mkt is 3.46E-08\n", "   max error for labor_mkt is 8.41E-10\n", "On iteration 4\n", "   max error for nkpc_res is 2.10E-11\n", "   max error for asset_mkt is 5.01E-10\n", "   max error for labor_mkt is 1.26E-11\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["rho_r, sig_r = 0.61, -0.01/4\n", "rstar_shock_path = {\"rstar\": sig_r * rho_r ** (np.arange(T))}\n", "\n", "td_nonlin = hank.solve_impulse_nonlinear(ss, unknowns, targets, rstar_shock_path)\n", "td_lin = hank.solve_impulse_linear(ss, unknowns, targets, rstar_shock_path)\n", "\n", "dC_nonlin = 100 * td_nonlin['C']\n", "dC_lin = 100 * td_lin['C']\n", "\n", "plt.plot(dC_lin[:21], label='linear', linestyle='-', linewidth=2.5)\n", "plt.plot(dC_nonlin[:21], label='nonlinear', linestyle='--', linewidth=2.5)\n", "plt.title(r'Consumption response to 1% monetary policy shock')\n", "plt.xlabel('quarters')\n", "plt.ylabel('% deviation from ss')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 A very large monetary policy shock\n", "However, the nonlinearities may become substantial for very large or persistent monetary policy shocks. Reassuringly, the Jacobian still works well as an updating rule. The quasi-Newton method for a 10% monetary policy shock (extremely large!) still converges below in just 9 iterations, despite nonlinearities evident in the results."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solving One-<PERSON><PERSON> HANK for ['pi', 'w', 'Y'] to hit ['nkpc_res', 'asset_mkt', 'labor_mkt']\n", "On iteration 0\n", "   max error for nkpc_res is 0.00E+00\n", "   max error for asset_mkt is 1.41E-01\n", "   max error for labor_mkt is 2.68E-02\n", "On iteration 1\n", "   max error for nkpc_res is 9.66E-05\n", "   max error for asset_mkt is 1.30E-02\n", "   max error for labor_mkt is 5.55E-04\n", "On iteration 2\n", "   max error for nkpc_res is 2.62E-05\n", "   max error for asset_mkt is 2.19E-03\n", "   max error for labor_mkt is 7.31E-04\n", "On iteration 3\n", "   max error for nkpc_res is 3.84E-06\n", "   max error for asset_mkt is 3.80E-04\n", "   max error for labor_mkt is 9.03E-05\n", "On iteration 4\n", "   max error for nkpc_res is 1.50E-06\n", "   max error for asset_mkt is 6.39E-05\n", "   max error for labor_mkt is 1.83E-05\n", "On iteration 5\n", "   max error for nkpc_res is 1.26E-07\n", "   max error for asset_mkt is 1.05E-05\n", "   max error for labor_mkt is 2.52E-06\n", "On iteration 6\n", "   max error for nkpc_res is 3.47E-08\n", "   max error for asset_mkt is 1.72E-06\n", "   max error for labor_mkt is 4.66E-07\n", "On iteration 7\n", "   max error for nkpc_res is 3.43E-09\n", "   max error for asset_mkt is 2.87E-07\n", "   max error for labor_mkt is 6.90E-08\n", "On iteration 8\n", "   max error for nkpc_res is 8.33E-10\n", "   max error for asset_mkt is 4.63E-08\n", "   max error for labor_mkt is 1.23E-08\n", "On iteration 9\n", "   max error for nkpc_res is 1.02E-10\n", "   max error for asset_mkt is 7.80E-09\n", "   max error for labor_mkt is 1.88E-09\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["rho_r, sig_r = 0.61, -0.10/4\n", "rstar_shock_path = {\"rstar\": sig_r * rho_r ** (np.arange(T))}\n", "\n", "td_nonlin = hank.solve_impulse_nonlinear(ss, unknowns, targets, rstar_shock_path)\n", "td_lin = hank.solve_impulse_linear(ss, unknowns, targets, rstar_shock_path)\n", "\n", "dC_nonlin = 100 * td_nonlin['C']\n", "dC_lin = 100 * td_lin['C']\n", "\n", "plt.plot(dC_lin[:21], label='linear', linestyle='-', linewidth=2.5)\n", "plt.plot(dC_nonlin[:21], label='nonlinear', linestyle='--', linewidth=2.5)\n", "plt.title(r'Consumption response to 10% monetary policy shock')\n", "plt.xlabel('quarters')\n", "plt.ylabel('% deviation from ss')\n", "plt.legend()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 2}