{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tutorial 2: The <PERSON><PERSON><PERSON><PERSON> model\n", "\n", "In this notebook we solve the <PERSON><PERSON><PERSON><PERSON> model from <PERSON><PERSON><PERSON>, <PERSON><PERSON>, R<PERSON><PERSON>, St<PERSON>ub (2021): \"Using the Sequence-Space Jacobian to Solve and Estimate Heterogeneous-Agent Models\" ([link to paper](https://www.bencebardoczy.com/publication/sequence-jacobian/sequence-jacobian.pdf)).\n", "\n", "New concepts:\n", "- **HA block**: represent heterogeneous agents at micro and macro level\n", "- **Unwrap GE Jacobians**: insights from step-by-step, manual construction\n", "- **Estimation**: from impulse responses to likelihood  \n", "\n", "For more examples and information on the SSJ toolkit, please visit our [GitHub page](https://github.com/shade-econ/sequence-jacobian)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import copy\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "from sequence_jacobian import het, simple, create_model              # functions\n", "from sequence_jacobian import interpolate, grids, misc, estimation   # modules"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1 Model description\n", "The model can be written in sequence space as\n", "$$\n", "\\textbf{F}_t(\\textbf{X}, Z) \\equiv \n", "\\begin{pmatrix}\n", "Y_t - Z_t K_{t-1}^\\alpha L_t^{1-\\alpha}\n", "\\\\\n", "r_t + \\delta - \\alpha Z_t \\left(\\frac{K_{t-1}}{L_t}\\right)^{\\alpha-1} \n", "\\\\\n", "w_t - (1-\\alpha) Z_t \\left(\\frac{K_{t-1}}{L_t}\\right)^{\\alpha}\n", "\\\\\n", "L_t - \\sum_e \\pi(e) e\n", "\\\\\n", "\\mathcal{K}_t(\\{r_s, w_s\\}) - K_t\n", "\\end{pmatrix}\n", "= \\begin{pmatrix}  0 \\\\ 0 \\\\ 0 \\\\ 0 \\\\ 0\\end{pmatrix},\n", "\\qquad t = 0, 1, \\dots\n", "$$\n", "\n", "where the (aggregate) endogenous variables are $\\textbf{X} = (Y, K, L, r, w)$ and the only exogenous variable is $Z$. Let's normalize $\\sum_e \\pi(e) e = 1$ without loss of generality.\n", "\n", "The capital function $\\mathcal{K}$ follows from the household block characterized by the <PERSON><PERSON> equation\n", "$$\n", "\\begin{align*}\n", "V_t(e, k_{-}) = \\max_{c, k} &\\left\\{\\frac{c^{1-\\sigma}}{1-\\sigma} + \\beta \\mathbb{E}_t\\left[V_{t+1}(e', k)|e \\right] \\right\\}\n", "\\\\\n", "c + k &= (1 + r_t)k_{-} + w_t e \n", "\\\\\n", "k &\\geq 0\n", "\\end{align*}\n", "$$\n", "\n", "We can represent this model as a Directed Acyclic Graph (DAG) in just one unknown $K$ and one target, asset market clearing. That is, we can write it as\n", "$$\n", "H_t(K, Z) \\equiv \\mathcal{K}_t\\left(\\left\\{\\alpha Z_s K_{s-1}^{\\alpha-1} - \\delta, (1-\\alpha) Z_s K_{s-1}^{\\alpha}\\right\\}\\right) - K_t = 0\n", "$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2 Set up heterogeneous-agent block\n", "\n", "Solving a HA problem consists of 3 distinct steps.\n", "1. **backward iteration**: solve for individual policy functions\n", "2. **forward iteration**: solve for distribution\n", "3. **aggregation**: obtain macro outcomes\n", "   \n", "Backward iteration is model specific, and requires careful consideration. Forward iteration is fairly mechanical. It depends only on the number and type (discrete or continuous) state variables. Aggregation is completely mechanical. As such, we ask users only to provide a recipe for backward iteration. Forward iteration and aggregation is handled automatically. \n", "\n", "So, the main task is to write a **backward step function** that represents the <PERSON><PERSON> equation. This has to be a single step of an iterative solution method such as value function iteration that solves for optimal policy on a grid. For the standard income fluctuation problem we're dealing with here, the endogenous gridpoint method (EGM) of [<PERSON> (2006)](https://www.sciencedirect.com/science/article/pii/S0165176505003368) is the best practice.\n", "- Formally, we need to write a function that maps the expected value of a \"backward variable\" tomorrow into the \"backward variable\" today, on the same grid. In the EGM, the backward variable is the partial of the value function with respect to assets, thus the backward step function is $f: \\mathbb{E}\\left[\\partial_k V(e', k)\\right|e] \\to \\partial_k V(e, k_{-}).$\n", "\n", "Once we have the backward step function, we can use the decorator `@het` to turn it into an instance of HetBlock. All we have to do is specify:\n", "- `exogenous`: name of the transition matrix for discrete exogenous state(s) (one or more, in forward order)\n", "- `policy`: name of policy corresponding to the continuous endogenous state(s) (one or two)\n", "- `backward`: name of backward variable(s) on which we're iterating (here the first derivative `Va` of the value function with respect to assets)\n", "- `backward_init`: a function that initializes a guess for the backward variable "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def household_init(a_grid, e_grid, r, w, eis):\n", "    coh = (1 + r) * a_grid[np.newaxis, :] + w * e_grid[:, np.newaxis]\n", "    Va = (1 + r) * (0.1 * coh) ** (-1 / eis)\n", "    return Va\n", "\n", "@het(exogenous='Pi', policy='a', backward='Va', backward_init=household_init)\n", "def household(Va_p, a_grid, e_grid, r, w, beta, eis):\n", "    \"\"\"Single backward iteration step using endogenous gridpoint method for households with CRRA utility.\n", "\n", "    Parameters\n", "    ----------\n", "    Va_p     : array (nE, nA), expected marginal value of assets next period\n", "    a_grid   : array (nA), asset grid\n", "    e_grid   : array (nE), producticity grid\n", "    r        : scalar, ex-post real interest rate\n", "    w        : scalar, wage\n", "    beta     : scalar, discount factor\n", "    eis      : scalar, elasticity of intertemporal substitution\n", "\n", "    Returns\n", "    ----------\n", "    Va : array (nE, nA), marginal value of assets today\n", "    a  : array (nE, nA), asset policy today\n", "    c  : array (nE, nA), consumption policy today\n", "    \"\"\"\n", "    uc_nextgrid = beta * Va_p\n", "    c_nextgrid = uc_nextgrid ** (-eis)\n", "    coh = (1 + r) * a_grid[np.newaxis, :] + w * e_grid[:, np.newaxis]\n", "    a = interpolate.interpolate_y(c_nextgrid + a_grid, coh, a_grid)\n", "    misc.setmin(a, a_grid[0])\n", "    c = coh - a\n", "    Va = (1 + r) * c ** (-1 / eis)\n", "    return Va, a, c"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Recall that every block (including HA blocks) represent a function in sequence space. HA blocks are special in that they have macro outputs (aggregates) but also micro outputs (distribution, policy functions)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<HetBlock 'household'>\n", "Inputs: ['a_grid', 'e_grid', 'r', 'w', 'beta', 'eis', 'Pi']\n", "Macro outputs: ['A', 'C']\n", "Micro outputs: ['D', 'Dbeg', 'Pi', 'Va', 'a', 'c']\n"]}], "source": ["print(household)\n", "print(f'Inputs: {household.inputs}')\n", "print(f'Macro outputs: {household.outputs}')\n", "print(f'Micro outputs: {household.internals}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The inputs of the `household` block include scalars (`w`, `r`, `eis`, `beta`) and arrays (`a_grid`, `e_grid`, `Pi`). A key assumption of the SSJ method is that blocks are only connected via scalars (scalar sequences). This ensures that all blocks have compatible Jacobians that can be accumulated along the DAG. Multidimensional inputs should be thought of as **internal** to a specific HetBlock. \n", "\n", "This convention is facilitated by `hetinput` and `hetoutput` functions. These are regular Python functions that we can attach to a HetBlock. The purpose of hetinputs is to map scalars (from outside the block) into the multidimensional inputs that the block needs. The purpose of hetoutputs is to map multidimensional outputs of the block (e.g. `a`, `c`) into new multidimensional outputs of interest. Think of it as follows. A HetBlock is evaluated in 3 steps.\n", "1. evaluate hetinputs; obtain multidimensional inputs\n", "2. evaluate core block (backward iteration); obtain policy functions\n", "3. evaluate hetoutputs; obtain other outputs of interest\n", "\n", "A single HetBlock may have zero or multiple `hetinputs` and `hetoutputs`. Moreover, hetinputs are allowed to depend on each other in an acyclic fashion. Same for hetoutputs.  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Continuing with our example, let's attach a hetinput function that creates the grid. Notice that `household_ext` does not have multidimensional inputs anymore. It knows how to make them internally, by calling `make_grid`. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<HetBlock 'household' with hetinput 'make_grid'>\n", "Inputs: ['r', 'w', 'beta', 'eis', 'rho_e', 'sd_e', 'nE', 'amin', 'amax', 'nA']\n"]}], "source": ["def make_grid(rho_e, sd_e, nE, amin, amax, nA):\n", "    e_grid, _, Pi = grids.markov_rouwenhorst(rho=rho_e, sigma=sd_e, N=nE)\n", "    a_grid = grids.agrid(amin=amin, amax=amax, n=nA)\n", "    return e_grid, Pi, a_grid\n", "\n", "\n", "household_ext = household.add_hetinputs([make_grid])\n", "\n", "print(household_ext)\n", "print(f'Inputs: {household_ext.inputs}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- The household block above is a special case of the generic \"standard incomplete markets model\" that we included with the toolkit in `sequence_jacobian/examples/hetblocks/household_sim.py`. \n", "- The rest of the blocks that constitute the <PERSON><PERSON><PERSON><PERSON> model are listed below but can also be found in `sequence_jacobian/examples/krusell_smith.py`."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["@simple\n", "def firm(K, L, Z, alpha, delta):\n", "    r = alpha * Z * (K(-1) / L) ** (alpha-1) - delta\n", "    w = (1 - alpha) * Z * (K(-1) / L) ** alpha\n", "    Y = Z * K(-1) ** alpha * L ** (1 - alpha)\n", "    return r, w, Y\n", "\n", "\n", "@simple\n", "def mkt_clearing(K, A, Y, C, delta):\n", "    asset_mkt = A - K\n", "    goods_mkt = Y - C - delta * K\n", "    return asset_mkt, goods_mkt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's put the model together."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['beta', 'eis', 'rho_e', 'sd_e', 'nE', 'amin', 'amax', 'nA', 'K', 'L', 'Z', 'alpha', 'delta']\n"]}], "source": ["ks = create_model([household_ext, firm, mkt_clearing], name=\"<PERSON><PERSON><PERSON><PERSON><PERSON>\")\n", "print(ks.inputs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 Calibrating the steady state\n", "Next, we calibrate the model in general equilibrium. The calibration exercise amounts to finding the right discount factor $\\beta$ to hit a targeted interest rate conditional on all the other exogenous parameters. We use <PERSON>'s method, an efficient way to solve for a root on an interval, from the `scipy.optimize` package to do so.\n", "\n", "Although additional efficiency gains would be possible here (for instance, by updating our initial guesses for policy and distribution along the way), we will not implement them, since they are not our focus here.\n", "\n", "Our default values depart slightly from the canonical <PERSON><PERSON><PERSON><PERSON><PERSON> calibration, mainly by assuming a 7-state income process and a lower capital share. More risk increases the precautionary savings motive, while less capital limits the ability to self-insure. These changes lead to higher MPCs and less RA-like behavior."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<SteadyStateDict: ['eis', 'delta', 'alpha', 'rho_e', 'sd_e', 'L', 'nE', 'nA', 'amin', 'amax', 'beta', 'Z', 'K', 'r', 'w', 'Y', 'A', 'C', 'asset_mkt', 'goods_mkt'], internals=['household']>\n"]}], "source": ["calibration = {'eis': 1, 'delta': 0.025, 'alpha': 0.11, 'rho_e': 0.966, 'sd_e': 0.5, 'L': 1.0,\n", "               'nE': 7, 'nA': 500, 'amin': 0, 'amax': 200}\n", "unknowns_ss = {'beta': 0.98, 'Z': 0.85, 'K': 3.}\n", "targets_ss = {'r': 0.01, 'Y': 1., 'asset_mkt': 0.}\n", "\n", "ss = ks.solve_steady_state(calibration, unknowns_ss, targets_ss, solver='hybr')\n", "\n", "print(ss)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's inspect the steady state.\n", "- the `solve_steady_state` method returned a `SteadyStateDict` object.\n", "- `ss` stores variables in two levels: *top* and *internal*\n", "- the top level contains scalars, which are accessible for all blocks of the model\n", "- the internal level contains block-specific multidimensional variables"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "\n", "Let's plot the consumption policy function for all skill types  as a function of assets."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.plot(ss.internals['household']['a_grid'], ss.internals['household']['c'].T)\n", "plt.xlabel('Assets'), plt.ylabel('Consumption')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Speed of steady-state solution\n", "Our backward and forward iterations use Numba, [a just-in-time compilation library](https://numba.pydata.org/numba-doc/dev/user/5minguide.html) that translates pure numerical Python to fast machine code. The first time functions are run, there is some compilation lag, similar to (but much less time-consuming than) compilation time in languages like C++, Fortran, and Julia.\n", "\n", "Now that we have run everything once, we can time our steady-state calibration routine. An efficient steady state is not the focus of this notebook or our methods, but it is nice to see that performance is still quite good:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 886 ms\n"]}], "source": ["%time ss = ks.solve_steady_state(calibration, unknowns_ss, targets_ss, solver=\"hybr\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Indeed, this simple example is so fast that increasing the number of asset gridpoints from 500 to 2000, and thus the total number of states from $7 \\times 500=3500$ to $7 \\times 2000 = 14000,$ increases computation time by less than a factor of four."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 1.63 s\n"]}], "source": ["calibration_highA = {**calibration, **{\"nA\": 2000}}\n", "\n", "%time _ = ks.solve_steady_state(calibration_highA, unknowns_ss, targets_ss, solver=\"hybr\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4 Jacob<PERSON>\n", "Jacobians are the key concept to solve and to estimate models in sequence space. Each model block has its own Jacobians, which can be combined via the chain rule into Jacobians for the complete model. A Jacobian in a $T$-dimensional (truncated) sequence space is a $T \\times T$ matrix of the form\n", "\n", "$$\n", "\\frac{d Y}{d X} = \n", "\\begin{pmatrix}\n", "\\frac{d Y_0}{d X_0} & \\frac{d Y_0}{d X_1} & \\dots & \\frac{d Y_0}{d X_{T-1}} \\\\\n", "\\vdots & \\vdots & \\ddots & \\vdots \\\\\n", "\\frac{d Y_{T-1}}{d X_0} & \\frac{d Y_{T-1}}{d X_1} & \\dots & \\frac{d Y_{T-1}}{d X_{T-1}}\n", "\\end{pmatrix}\n", "$$\n", "\n", "evaluated at the steady state. Every column can be interpreted as the impulse response to a one-period news shock.\n", "\n", "### 4.1 Simple blocks\n", "To build intuition, let's start with the firm block we instantiated above. In our code, simple blocks are specified as regular Python functions with the added decorator ``@simple``. In the body of the function, we directly implement the corresponding equilibrium conditions. The decorator turns the function into an instance of ``SimpleBlock``, a class that, among other things, knows how to handle time displacements such as `K(-1)` to denote 1-period lags and `r(+1)` to denote 1-period leads. In general, one can write (-s) and (+s) to denote s-period lags and leads."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Blocks can compute their Jacobians by the `jacobian` method. This takes in the `SteadyStateDict` object returned by the `solve_steady_state` method and two optional inputs: the truncation horizon and list of variables to differentiate with respect to. It returns the Jacobians in a nested dict, where the first level is the output variable $Y$ and the second level is the input variable $X$."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<JacobianDict outputs=['r', 'w', 'Y'], inputs=['K', 'Z']>\n"]}], "source": ["J_firm = firm.jacobian(ss, inputs=['K', 'Z'])\n", "\n", "print(J_firm)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By default, `jacobian` compoutes the Jacobian for each input-output pair. In practice, it only makes sense to do so with respect to endogenous variables and shocks, hence the `inputs` option. In this model, capital and TFP are the only inputs that will ever change.\n", "\n", "The Jacobian is diagonal because the production function does not depend on leads or lags of productivity. Such sparsity is very common for simple blocks, and we wrote the SimpleBlock class to take full advantage of it. For example, if we leave the truncation parameter $T$ unspecified, which is recommended, `jacobian` returns a more efficient sparse representation of the Jacobian."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1.13424196 0.         0.        ]\n", " [0.         1.13424196 0.        ]\n", " [0.         0.         1.13424196]]\n", "\n", "\n", "SimpleSparse({(0, 0): 1.134})\n"]}], "source": ["print(J_firm['Y']['Z'].matrix(3))\n", "print('\\n')\n", "print(J_firm['Y']['Z'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The output means that the only non-zero element is 1.134 along the main diagonal."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 HA blocks\n", "HA blocks have more complicated Jacobians, but they have a regular structure that we can exploit to calculate them very quickly. For comprehensive coverage of our **fake news algorithm**, please see the paper.\n", "\n", "A `HetBlock` object has a `jacobian` method that is analogous to one above for `SimpleBlock` objects."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 0.09578534 -0.68179015 -0.64119935 -0.60434415 -0.57057436]\n", " [ 0.09413756  0.13463153 -0.64841526 -0.61133486 -0.5771951 ]\n", " [ 0.09250592  0.12963223  0.16651934 -0.61980277 -0.58527967]\n", " [ 0.09087539  0.12509918  0.15952962  0.1938437  -0.59485549]\n", " [ 0.08926245  0.12116257  0.15312513  0.18541035  0.21770361]]\n"]}], "source": ["J_ha = household.jacobian(ss, inputs=['r', 'w'], T=5)\n", "print(J_ha['C']['r'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Notice that this matrix is no longer sparse. This generally the case for HA blocks. The Bellman equation implies that policies are forward-looking, and then aggregates are also backward-looking due to persistence coming via the distribution.\n", "\n", "Our `SimpleSparse` Jacobian objects are conformable with standard `np.array` objects, so that we can easily combine the Jacobians of simple blocks and HA blocks. For example, the multiplication operator `@` maps any combination of SimpleSparse and `np.array` objects into `np.array` objects as in standard `np.array` matrix multiplication.   "]}, {"cell_type": "code", "execution_count": 14, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 0.00380253 -0.02706602 -0.02545463 -0.02399154 -0.02265093]\n", " [ 0.00373712  0.00534467 -0.02574109 -0.02426906 -0.02291376]\n", " [ 0.00367234  0.0051462   0.00661056 -0.02460522 -0.02323471]\n", " [ 0.00360761  0.00496625  0.00633308  0.0076953  -0.02361485]\n", " [ 0.00354358  0.00480997  0.00607883  0.00736051  0.0086425 ]]\n"]}], "source": ["print(J_ha['C']['r'] @ J_firm['r']['Z'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5 Linearized dynamics using Jacobians\n", "Recall that we reduced the model to a single equation $H$\n", "\n", "$$\n", "H_t(K, Z) \\equiv \\mathcal{K}_t\\left(\\left\\{\\alpha Z_s K_{s-1}^{\\alpha-1} - \\delta, (1-\\alpha) Z_s K_{s-1}^{\\alpha}\\right\\}\\right) - K_t = 0.\n", "$$\n", "\n", "to be solved for $K$ given any $Z$.\n", "\n", "This is the composition of the household capital supply function $\\mathcal{K}$ with the interest rate $r(K_{t-1}, Z_t) = \\alpha Z_t K_{t-1}^{\\alpha-1} - \\delta$ and wage $w(K_{t-1}, Z_t) = (1-\\alpha) Z_t K_{t-1}^{\\alpha}$ functions.\n", "\n", "We will obtain a linear characterization of all impulse responses in two ways:\n", "- First, by duly following the notation and algebra of section 2.2 in the paper. This hands-on approach is transparent, but error-prone in all but the simplest cases.\n", "- Second, by leveraging the powerful convenience functions that we introduced in the RBC notebook, and further employ in the one-asset and two-asset HANK notebooks. Throughout this section, we'll use the steady state `ss` we've already solved for, and a 300-period truncation horizon."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.1 Hands-on approach\n", "\n", "##### Step 1: For each block, obtain Jacobians of outputs with respect to inputs\n", "Here, we only have two blocks: the firm and household blocks. Getting these Jacobians is quite quick, even for the HA block."]}, {"cell_type": "code", "execution_count": 15, "metadata": {"scrolled": true}, "outputs": [], "source": ["# Import the JacobianDict class, since we are manually constructing Jacobians to demonstrate how the automatic construction\n", "# works in the code\n", "from sequence_jacobian.classes import JacobianDict\n", "\n", "# firm Jacobian: r and w as functions of K and Z\n", "J_firm = firm.jacobian(ss, inputs=['K', 'Z'])\n", "\n", "# household Jacobian: curlyK (called 'a' for assets by <PERSON>_<PERSON>) as function of r and w\n", "T = 300\n", "J_ha = household.jacobian(ss, inputs=['r', 'w'], T=T)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 2: Compose <PERSON> along the DAG\n", "Here, we manually apply the chain rule to calculate the total Jacobians of household $\\mathcal{K}$ with respect to $K$ and $Z$, by composing the partial Jacobians `J_ha` (which maps $r$ and $w$ to $\\mathcal{K}$) and `J_firm` (which maps $K$ and $Z$ to $r$, $w$, and $Y$)."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["J_curlyK_K = J_ha['A']['r'] @ J_firm['r']['K'] + J_ha['A']['w'] @ J_firm['w']['K']\n", "J_curlyK_Z = J_ha['A']['r'] @ J_firm['r']['Z'] + J_ha['A']['w'] @ J_firm['w']['Z']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we will combine `<PERSON>_<PERSON><PERSON>_<PERSON>` and `J_curly<PERSON>_Z` with the firm Jacobians to get a single nested dict with the total Jacobians of everything with respect to $K$ and $Z$:"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["J = copy.deepcopy(J_firm)\n", "J.update(JacobianDict({'curlyK': {'K' : J_curly<PERSON>_K, 'Z' : J_<PERSON><PERSON>_<PERSON>}}))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 3: Invert $H_K$ to obtain impulse response $dK=-H_K^{-1}H_ZdZ$\n", "Now we need to get the Jacobians of our equation $H(K,Z)$, which fully characterizes equilibrium, with respect to $K$ and $Z$. Once we have these, the implicit function theorem gives us the endogenous capital $dK=-H_K^{-1}H_ZdZ$ response to any exogenous shock $dZ$.\n", "\n", "Recall that $H$ equals household capital supply $\\mathcal{K}$ minus firm capital $K$, so that we can get the Jacobians almost immediately from what we have already calculated."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["H_K = J['curlyK']['K'] - np.eye(T)\n", "H_Z = J['curlyK']['Z']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We define $G_K \\equiv -H_K^{-1}H_Z$ to be the matrix mapping from any $dZ$ to the corresponding $dK$:"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["G = {'K': -np.linalg.solve(H_K, H_Z)}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 4: Get all other impulses\n", "In `J`, we have Jacobians of $r,w,Y$ with respect to both $Z$ and $K$. If there is a shock to $Z$, we can decompose the change in $r,w,Y$ into a \"direct\" effect from $dZ$ and an \"indirect\" effect from the induced change $dK$.\n", "\n", "We apply the chain rule to the latter to get matrices $G_r, G_w, G_Y$ mapping any $dZ$ to the corresponding $dr,dw,dY$:"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["G['r'] = J['r']['Z'] + J['r']['K'] @ G['K']\n", "G['w'] = J['w']['Z'] + J['w']['K'] @ G['K']\n", "G['Y'] = J['Y']['Z'] + J['Y']['K'] @ G['K']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `G` dict characterizes the full linearized dynamics of aggregate variables $K, r, w, Y$ in response to *any* perturbation to $Z$.\n", "\n", "If we want the linearized dynamics of other variables, e.g. consumption, all we need is to get the Jacobian from the block in which these are calculated, and then compose it with the `G` of its inputs. It turns out that consumption was already reported in `J_ha`, so we just need to write:"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["G['C'] = J_ha['C']['r'] @ G['r'] + J_ha['C']['w'] @ G['w']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Convenient approach\n", "\n", "The DAG of the model contains sufficient information to automate this process. The only other things we need to know are the steady state around which to linearize, and the truncation horizon."]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["inputs = ['Z']\n", "unknowns = ['K']\n", "targets = ['asset_mkt']\n", "\n", "G2 = ks.solve_jacobian(ss, unknowns, targets, inputs, T=T)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's verify that the two give the same answer up to high precision (`assert` throws an error if its argument evaluates to False)."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["for o in G:\n", "    assert np.allclose(G2[o]['Z'], G[o])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.3 Results\n", "What do the impulse responses in this model look like? The beauty of our method is that, now that we have $G$, we can compute impulse responses to any shock almost instantaneously -- it's just matrix multiplication!\n", "\n", "Let's first consider shocks to $Z$ with different persistences, all normalized so that they have a 1% initial impact, and plot the interest rate response to each."]}, {"cell_type": "code", "execution_count": 24, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["rhos = np.array([0.2, 0.4, 0.6, 0.8, 0.9])\n", "dZ = 0.01 * ss['Z'] * rhos ** (np.arange(T)[:, np.newaxis]) # get T*5 matrix of dZ\n", "dr = G['r'] @ dZ\n", "\n", "plt.plot(10000*dr[:50, :])\n", "plt.title(r'$r$ response to 1% $Z$ shocks with $\\rho=(0.2 ... 0.9)$')\n", "plt.ylabel(r'basis points deviation from ss')\n", "plt.xlabel(r'quarters')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's consider 10,000 different values of rho, for which we can still calculate all $r$ impulse responses almost instantaneously."]}, {"cell_type": "code", "execution_count": 25, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 15 ms\n"]}], "source": ["rhos = np.linspace(0.1, 0.9, 10000)\n", "dZ = 0.01 * ss['Z'] * rhos ** (np.arange(T)[:, np.newaxis]) # get T*5 matrix of dZ\n", "%time dr = G['r'] @ dZ"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The time taken here differs from run to run, but in the current run (like all runs on a personal laptop) it takes less than 20 milliseconds of \"wall time\" to compute 10,000 impulse responses means that each impulse response takes less than **2 microseconds**. \"CPU time\" is slightly higher because mild parallelization on two cores is used by the built-in matrix multiplication implementation. By contrast, typical impulse response calculations in heterogeneous agent models in the literature take at least a minute, so this method is more than **30 million** times faster as a way of calculating individual impulse responses.\n", "\n", "Although this may seem like an extreme example, repeated calculations of this form are quite useful in the most computationally demanding applications, like estimation (as we will see later).\n", "\n", "Another important feature of our sequence space methodology is that it is easy to calculate the response to shocks that are difficult to cast into simple recursive form, like news shocks.\n", "\n", "For example, calculating the response to a news shock where $Z$ is expected to increase at a specific period in the future is trivial -- in fact, that's exactly what the columns of the $G$ matrix are. Below we plot the capital responses to news shocks of $Z$ increases at periods 5, 10, 15, 20, and 25."]}, {"cell_type": "code", "execution_count": 26, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["dZ = 0.01*(np.arange(T)[:, np.newaxis] == np.array([5, 10, 15, 20, 25]))\n", "dK = G['K'] @ dZ\n", "\n", "plt.plot(dK[:50])\n", "plt.title('$K$ response to 1% Z news shocks for $t=5,...,25$')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6 Estimation\n", "\n", "The roadmap for estimation is as follows:\n", " - impulse responses $\\implies$ second moments\n", " - second moments $\\implies$ log-likelihood\n", " \n", "In this notebook, we will simply use simulated data to show that the log-likelihood peaks close to the true value of the estimated parameter, the standard deviation of TFP shocks. \n", "\n", "### 6.1 Second moments \n", "Thanks to certainty equivalence, linear impulse responses can be interpreted as the $MA(\\infty)$ representation of the model. Then all we need is the second moments of the shocks to fully characterize the second moments of endogenous outcomes. This computation can be done in a highly efficient way, without any need for simulation.\n", "\n", "For concreteness, let's suppose that the TFP shocks $dZ$ in the Krusell-Smith model have a persistent and transitory component.\n", "\n", "$$\n", "\\begin{align*}\n", "dZ_t &= dZ^1_t + d Z_t^2\n", "\\\\\n", "dZ_t^1 &= \\rho d Z_{t-1}^1 + \\epsilon^1_t\n", "\\\\\n", "d Z_t^2 &= \\epsilon^2_t\n", "\\end{align*}\n", "$$\n", "\n", "where $\\epsilon^1$ and $\\epsilon^2$ are i.i.d. normal with mean zero and with variance $\\sigma^2_1$ and $\\sigma^2_2$. \n", "\n", "Let $m^{x, 1}$ and $m^{x,2}$ denote the (truncated) impulse responses of variable $X$ to these two structural shocks. Then we have an $MA(T-1)$ representation:\n", "\n", "$$\n", "X_t = \\sum_{z=1}^{2} \\sum_{s=0}^{T-1} m^{x,z}_s \\epsilon_{t-s}^z \n", "$$\n", "\n", "It follows that covariances between outcomes $Y$ and $X$ take the form\n", "\n", "$$\n", "\\text{Cov}(Y_t, X_{t+l})= \\sum_{z=1}^{2} \\sigma_z^2 \\sum_{s=0}^{T-1} m_s^{x,z} m_{s+l}^{y,z} \\tag{Cov}\n", "$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 1. Stacked impulse responses\n", "\n", "Given the $G$ matrix we calculated in section 4, this is just a matter of matrix multiplication:"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["rho = 0.9\n", "sigma_persist = 0.1\n", "sigma_trans = 0.2\n", "\n", "dZ1 = rho**(np.arange(T))\n", "dY1, dC1, dK1 = G['Y'] @ dZ1, G['C'] @ dZ1, G['K'] @ dZ1\n", "dX1 = np.stack([dZ1, dY1, dC1, dK1], axis=1)\n", "\n", "dZ2 = np.arange(T) == 0\n", "dY2, dC2, dK2 = G['Y'] @ dZ2, G['C'] @ dZ2, G['K'] @ dZ2\n", "dX2 = np.stack([dZ2, dY2, dC2, dK2], axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's stack all these to make a $T\\times 4 \\times 2$ array giving all impulse responses of the four outcomes to the two shocks:"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/plain": ["(300, 4, 2)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["dX = np.stack([dX1, dX2], axis=2)\n", "dX.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 2. Obtain covariance at all leads and lags\n", "\n", "The covariance formula above is implemented efficiently using the Fast Fourier Transform by the `all_covariances` function, which returns a $T\\times 4\\times 4$ array `Sigma`, where `Sigma[l, o1, o2]` gives the covariance for any $t$ between output `o1` at $t$ and output `o2` at $t+l$."]}, {"cell_type": "code", "execution_count": 29, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 0 ns\n"]}], "source": ["sigmas = np.array([sigma_persist, sigma_trans])\n", "Sigma = estimation.all_covariances(dX, sigmas) # burn-in for jit\n", "%time Sigma = estimation.all_covariances(dX, sigmas)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Visualizing correlations\n", "\n", "Let's turn the covariance matrix `Sigma` into a correlation matrix, by normalizing it with the standard deviation of each series:"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"scrolled": true}, "outputs": [], "source": ["sd = np.sqrt(np.diag(Sigma[0, ...]))\n", "correl = (Sigma/sd)/(sd[:, np.newaxis])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, let's plot $\\text{corr}(dZ_t,dZ_{t+l})$, $\\text{corr}(dZ_t,dY_{t+l})$, $\\text{corr}(dZ_t,dC_{t+l})$, and $\\text{corr}(dZ_t,dK_{t+l})$, for $l$ from $-50$ through $50$: the correlations of all series, at various lags, with the driving productivity process."]}, {"cell_type": "code", "execution_count": 31, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["ls = np.arange(-50, 51)\n", "corrs_l_positive = correl[:51, 0, :]\n", "corrs_l_negative = correl[50:0:-1, :, 0]\n", "corrs_combined = np.concatenate([corrs_l_negative, corrs_l_positive])\n", "\n", "plt.plot(ls, corrs_combined[:, 0], label='dZ')\n", "plt.plot(ls, corrs_combined[:, 1], label='dY')\n", "plt.plot(ls, corrs_combined[:, 2], label='dC')\n", "plt.plot(ls, corrs_combined[:, 3], label='dK')\n", "plt.legend()\n", "plt.title(r'Corr of $dZ_t$ and $X_{t+l}$ for various series $X$')\n", "plt.xlabel(r'Lag $l$')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here we can see many features of the solution. First, $dY$ is extremely similar to $dZ$, except that it has a greater lagged response due to capital accumulation. Both $dZ$ and $dY$ have a large peak at $l=0$ corresponding to the transitory shock. $dC$ and $dK$, on the other hand, are both much smoother and tend to lag $dZ$, especially $dK$."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.2 Log-likelihood\n", "\n", "Assuming the means of all series are zero (which is often imposed through some kind of preliminary demeaning or detrending procedure on the data), and that the shocks are multivariate normal, all we really need to evaluate the log-likelihood are the second moments and the data.\n", "\n", "Specifically, if our data observations are stacked in a vector $\\mathbf{y}$ and the covariances at all lags are stacked in a matrix $\\mathbf{V}$, then the log-likelihood is (up to a constant):\n", "\n", "$$\n", "\\mathcal{L} = -\\frac{1}{2}\\log(\\det(\\mathbf{V})) - \\frac{1}{2}\\mathbf{y}'\\mathbf{V}^{-1}\\mathbf{y} \\tag{log-likelihood}\n", "$$ \n", "\n", "Our code proceeds directly from the covariances `Sigma` at all lags returned by `all_covariances` in the last section, and the matrix of data series, implementing (log-likelihood) under the hood to calculate the log-likelihood."]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 999 µs\n"]}, {"data": {"text/plain": ["-50403.98854011552"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# random 100 observations\n", "Y = np.random.randn(100, 4)\n", "\n", "# 0.05 measurement error in each variable\n", "sigma_measurement = np.full(4, 0.05)\n", "\n", "# calculate log-likelihood\n", "estimation.log_likelihood(Y, Sigma, sigma_measurement)\n", "\n", "%time estimation.log_likelihood(Y, Sigma, sigma_measurement)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 6.3 Putting it together: calculating the likelihood in an example\n", "Continue to consider the shock process from section 5.1, where $dZ$ had one persistent component and one transitory component. Imagine that we do not know the persistence parameter $\\rho$ of the persistent component, nor do we know the relative variances of these two shocks or the variances of the measurement errors. Given some observed data $\\mathbf{w}$ with 100 observations of $dZ, dY, dC, dY$, what does the log-likelihood as a function of these parameters look like, and how do we compute it?\n", "\n", "We write the following function."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["def log_likelihood_from_parameters(rho, sigma_persist, sigma_trans, sigma_measurement, Y):\n", "    # impulse response to persistent shock\n", "    dZ1 = rho**(np.arange(T))\n", "    dY1, dC1, dK1 = G['Y'] @ dZ1, G['C'] @ dZ1, G['K'] @ dZ1\n", "    dX1 = np.stack([dZ1, dY1, dC1, dK1], axis=1)\n", "    \n", "    # since transitory shock does not depend on any unknown parameters,\n", "    # except scale sigma_trans, we just reuse the dX2 already calculated earlier!\n", "    \n", "    # stack impulse responses together to make MA(T-1) representation 'M'\n", "    M = np.stack([dX1, dX2], axis=2)\n", "    \n", "    # calculate all covariances\n", "    Sigma = estimation.all_covariances(M, np.array([sigma_persist, sigma_trans]))\n", "    \n", "    # calculate log=likelihood from this\n", "    return estimation.log_likelihood(Y, Sigma, sigma_measurement)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Simulate sample data.** Now let's simulate more realistic series $w=\\{dZ, dY, dC, dK\\}$, assuming the same parameters as above, to see what evaluating the log-likelihood is like."]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["# stack covariances into matrix using helper function, then do a draw using NumPy routine\n", "V = estimation.build_full_covariance_matrix(Sigma, sigma_measurement, 100)\n", "Y = np.random.multivariate_normal(np.zeros(400), V).reshape((100, 4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's plot the log-likelihood of $w$ as a function of the standard deviation of the persistent component (the true value of which is 0.1), given correct values for all other parameters. Note that evaluating the log-likelihood 100 times takes well below one second."]}, {"cell_type": "code", "execution_count": 35, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 195 ms\n"]}], "source": ["sigma_persist_values = np.linspace(0.05, 0.2, 100)\n", "%time lls = np.array([log_likelihood_from_parameters(rho, sigma_persist, sigma_trans, sigma_measurement, Y) for sigma_persist in sigma_persist_values])"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.plot(sigma_persist_values, lls)\n", "plt.axvline(0.1, linestyle=':', color='gray')\n", "plt.title(r'Log likelihood of simulated data as function of $\\sigma_{persist}$')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Reassuringly, the mode is near (although not exactly at, since we're simulating a finite sample with only 100 periods) the value of $\\sigma_{persist}=0.1$ with which the data was simulated!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8 Nonlinear perfect foresight dynamics\n", "Linearized impulse responses are invariant to the sign and size of aggregate shocks by construction. Although this is not a problem for many applications, it is clearly a limitation. Here, we demonstrate that the Jacobian is also extremely useful in solving for nonlinear dynamics.\n", "\n", "Consider the case of an economy that starts at the steady state, and receives an unexpected shock at date 0. This is often called an \"MIT shock\". \n", "\n", "For a given shock $Z$, the solution is still characterized by the $K$ that solves $H(K,Z)=0$. Our algorithm to find this $K$, a simple variant of <PERSON>'s method, iterates on a sequence of guesses $K^j$ for the path of capital as follows:\n", "1. starting with $j=0$, guess a path $K^0$\n", "2. calculate $H(K^j, Z)$\n", "3. update the guess according to $$K^{j+1} = K^j - H_K(K_{ss}, Z_{ss})^{-1} H(K^j, Z)$$\n", "and return to step 2 for $j+1$\n", "\n", "The only difference between this and the standard Newton's method is that we use the Jacobian $H_K$ around the steady state (which we have already calculated), rather than the Jacobian around the current guess (which is somewhat more difficult to calculate). Still, this tends to achieve extremely fast convergence.\n", "\n", "Although iterative methods to solve for equilibrium in response to MIT shocks are quite common in the heterogeneous-agent literature, these methods generally use ad-hoc rules for updating the guess, which can be fragile and slow. In applications with no or limited heterogeneity (such as spender-saver models) where it is possible to calculate Jacobians easily, <PERSON>'s method has been used to provide faster and more robust convergence -- see, for instance, [<PERSON><PERSON><PERSON> (1996)](http://www.cepremap.fr/depot/couv_orange/co9602.pdf).\n", "\n", "But until now, this has not been applied to heterogeneous-agent models, since the Jacobian has been too difficult to calculate. The only partial applications to date, to our knowledge, are in [<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (2018)](http://web.stanford.edu/~aauclert/computational_appendix_inequad.pdf), which uses an auxiliary model to compute an approximate Jacobian, and in [<PERSON><PERSON><PERSON> (2018)](https://scholar.harvard.edu/files/straub/files/jmp_straub_jan_2.pdf), which uses interpolation to compute an approximate Jacobian. In this paper, however, we have provided a highly efficient method to compute the *exact* Jacobian around the steady state for any heterogeneous-agent model, and can thus readily apply <PERSON>'s method."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.1 Implementation\n", "\n", "Our quasi-Newton method can be implemented in two steps. First, build the nonlinear function $H(U, Z).$ Second, guess $U$ for a given $Z$ and iterate until convergence. We automatized both of these, so all we need to do is call the `solve_impulse_nonlinear` method for the `ks` object. We will also solve for the linearized dynamics using the `solve_impulse_linear` method for comparison. \n", "\n", "Note that both the shock and the results (in an `ImpulseDict` object) are expressed in deviations from ss."]}, {"cell_type": "code", "execution_count": 37, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solving <PERSON>-<PERSON> for ['K'] to hit ['asset_mkt']\n", "On iteration 0\n", "   max error for asset_mkt is 3.37E-02\n", "On iteration 1\n", "   max error for asset_mkt is 5.73E-05\n", "On iteration 2\n", "   max error for asset_mkt is 1.26E-07\n", "On iteration 3\n", "   max error for asset_mkt is 1.75E-10\n", "<ImpulseDict: ['Z', 'K', 'A', 'C', 'r', 'w', 'Y', 'asset_mkt', 'goods_mkt'], internals=['household']>\n"]}], "source": ["Z_shock_path = {\"Z\": 0.01*0.8**np.arange(T)}\n", "\n", "td_nonlin = ks.solve_impulse_nonlinear(ss, unknowns, targets, Z_shock_path)\n", "td_lin = ks.solve_impulse_linear(ss, unknowns, targets, Z_shock_path)\n", "\n", "print(td_nonlin)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We see that this is quite quick, taking only 3 iterations after the initial guess to achieve high accuracy -- far fewer than typical ad-hoc updating rules, which can take hundreds or even thousands of iterations.\n", "\n", "Now let's compare to the results we'd get for the linearized impulse response, and plot the paths for the real interest rate $r$."]}, {"cell_type": "code", "execution_count": 38, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["dr_nonlin = 10000 * td_nonlin['r']\n", "dr_lin = 10000 * td_lin['r']\n", "\n", "plt.plot(dr_nonlin[:50], label='nonlinear', linewidth=2.5)\n", "plt.plot(dr_lin[:50], label='linear', linestyle='--', linewidth=2.5)\n", "plt.title(r'Interest rate response to a 1% TFP shock')\n", "plt.ylabel(r'bp deviation from ss')\n", "plt.xlabel(r'quarters')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There is no visible difference -- the first-order approximation is almost perfect here.\n", "\n", "If we increase the size of the productivity shock on impact to 10% (enormous!), we can begin to see small nonlinearities kick in, and the solution requires five iterations rather than three:"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solving <PERSON>-<PERSON> for ['K'] to hit ['asset_mkt']\n", "On iteration 0\n", "   max error for asset_mkt is 3.47E-01\n", "On iteration 1\n", "   max error for asset_mkt is 5.22E-03\n", "On iteration 2\n", "   max error for asset_mkt is 1.16E-04\n", "On iteration 3\n", "   max error for asset_mkt is 1.69E-06\n", "On iteration 4\n", "   max error for asset_mkt is 1.13E-08\n", "On iteration 5\n", "   max error for asset_mkt is 2.95E-10\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["big_Z_shock_path = {\"Z\": 0.1*0.8**np.arange(T)}\n", "\n", "td_nonlin = ks.solve_impulse_nonlinear(ss, unknowns, targets, big_Z_shock_path)\n", "td_lin = ks.solve_impulse_linear(ss, unknowns, targets, big_Z_shock_path)\n", "\n", "# extract interest rate response, scale to basis points\n", "dr_nonlin = 10000 * td_nonlin['r']\n", "dr_lin = 10000 * td_lin['r']\n", "\n", "plt.plot(dr_nonlin[:50], label='nonlinear', linewidth=2.5)\n", "plt.plot(dr_lin[:50], label='linear', linestyle='--', linewidth=2.5)\n", "plt.title(r'$r$ response to 10% $Z$ shock')\n", "plt.ylabel(r'bp deviation from ss')\n", "plt.xlabel(r'quarters')\n", "plt.legend()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 2}