{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tutorial 5: Labor search\n", "\n", "The most-requested feature of SSJ toolkit v0.1 has been support for time-varying transition matrices for exogenous states in HA blocks. This is needed, for example, for models in which the probability of becoming unemployed varies with the business cycle. This feature is supported by HetBlocks in 1.0.0 and newer versions of the toolkit. In this notebook, we show how to implement it. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1 Model setup\n", "\n", "Consider a simple incomplete markets model with unemployment risk. The state variables are \n", "- employment status $s \\in \\{E, U\\}$\n", "- labor productivity $e \\in \\{e_1, \\dots, e_m\\}$\n", "- liquid assets $a \\in [\\underline{a}, \\infty)$ \n", "\n", "Employment status matters for income. Employed workers earn wage $w_t e$, while unemployed workers receive benefits $b e.$ Let $y_t(s, e)$ denote this non-financial income.\n", "\n", "From the workers' perspective, employment status evolves exogenously. An employed worker may lose her job with probability $\\sigma_t$. An unemployed worker may find a job with probability $f_t.$ Let $\\Pi_t^s$ denote the Markov matrix associated with state $s$. Of course, these transition probabilities may be determined endogenously outside the HetBlock, e.g. in a search and matching block.\n", "\n", "The <PERSON><PERSON> equation is\n", "$$\n", "\\begin{align*}\n", "V_t(s, e, a_{-}) = \\max_{c, a} &\\left\\{\\frac{c^{1-\\sigma}}{1-\\sigma} + \\beta \\mathbb{E}_t\\left[V_{t+1}(s', e', a)|s, e \\right] \\right\\}\n", "\\\\\n", "c + a &= (1 + r_t)a_{-} + y_t(s, e)\n", "\\\\\n", "a &\\geq \\underline{a}\n", "\\end{align*}\n", "$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2 Set up HA block\n", "\n", "Starting in versio 1.0.0, HetBlocks support an arbitrary number of discrete exogenous state variables. The only restriction is on timing. We assume that when agents choose their endogenous state(s) (in this case assets $a$), all discrete shocks have already been resolved. The <PERSON>man equation reflects this timing assumption. The relevant state is `(s, e, a_{-})`, i.e. `(s, e)` are from the current period, while `a_{-}` is from last period.\n", "\n", "Some specific assumption on timing is necessary for the HetBlock to know how to take expectations with respect to exogenous states (backward iteration) and how to construct the law of motion for the distribution (forward iteration). The one we chose (discrete shocks before decisions) is the most common in the macro models.\n", "\n", "In this paradigm, the HA problem we're dealing with is just a special case of SIM model that we used in the Krusell-<PERSON> model. Compared to `sequence_jacobian/examples/hetblocks/household_sim`, we just need to make 3 small changes.\n", "1. Make sure that numpy expressions work for 3-dimensional arrays (state space is `(s, e, a)` and not `(e, a)`).\n", "2. Provide names of `exogenous` <PERSON><PERSON> matrices in *chronological order*.\n", "3. Write hetinput function that maps job-finding and separation rates into the Markov matrix $\\Pi_t^s$.\n", "\n", "Once this is done. Everything will work as expected. We can shock and compute Jacobians with respect to $f_t$ and so on. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 1: hetin<PERSON>s "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from sequence_jacobian import markov_r<PERSON><PERSON><PERSON><PERSON>, agrid\n", "\n", "def make_grid(rho_e, sd_e, nE, amin, amax, nA):\n", "    e_grid, _, Pi_e = markov_rouwenhorst(rho=rho_e, sigma=sd_e, N=nE)\n", "    a_grid = agrid(amin=amin, amax=amax, n=nA)\n", "    return e_grid, Pi_e, a_grid\n", "\n", "\n", "def search_frictions(f, s):\n", "    Pi_s = np.vstack(([1 - s, s], [f, 1 - f]))\n", "    return Pi_s\n", "\n", "\n", "def labor_income(e_grid, w, b):\n", "    y = np.vstack((w * e_grid, b * e_grid))\n", "    return y "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 2: core HA block"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from sequence_jacobian import het, interpolate_y\n", "\n", "def household_init(a_grid, y, r, eis):\n", "    c = np.maximum(1e-8, y[..., np.newaxis] + np.maximum(r, 0.04) * a_grid)\n", "    Va = (1 + r) * (c ** (-1 / eis))\n", "    return Va\n", "\n", "@het(exogenous=['Pi_s', 'Pi_e'], policy='a', backward='Va', backward_init=household_init)\n", "def household(Va_p, a_grid, y, r, beta, eis):\n", "    c_nextgrid = (beta * Va_p) ** (-eis)\n", "    coh = (1 + r) * a_grid + y[..., np.newaxis]\n", "    a = interpolate_y(c_nextgrid + a_grid, coh, a_grid)  # (x, xq, y)\n", "    a = np.maximum(a, a_grid[0]) \n", "    c = coh - a\n", "    uc = c ** (-1 / eis)\n", "    Va = (1 + r) * uc\n", "\n", "    return Va, a, c"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 4: hetoutputs\n", "\n", "Let's report the unemployment rate."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def unemployment(c):\n", "    u = np.zeros_like(c)\n", "    u[1, ...] = 1.0\n", "    return u"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 4: assemble HA block"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<HetBlock 'household' with hetinput 'search_frictions_labor_income' and with hetoutput `unemployment'>\n", "Inputs: ['r', 'beta', 'eis', 'rho_e', 'sd_e', 'nE', 'amin', 'amax', 'nA', 'f', 's', 'w', 'b']\n", "Macro outputs: ['A', 'C', 'U']\n", "Micro outputs: ['D', 'Dbeg', 'Pi_s', 'Pi_e', 'Va', 'a', 'c', 'u', 'e_grid', 'a_grid', 'y']\n"]}], "source": ["hh = household.add_hetinputs([make_grid, search_frictions, labor_income])\n", "hh = hh.add_hetoutputs([unemployment])\n", "\n", "print(hh)\n", "print(f'Inputs: {hh.inputs}')\n", "print(f'Macro outputs: {hh.outputs}')\n", "print(f'Micro outputs: {hh.internals}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 Use HA block\n", "\n", "Let's consider a baseline calibration and a shock to the separation rate."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["calibration = dict(beta=0.95, r=0.01, eis=0.5, f=0.4, s=0.1, w=1., b=0.5,\n", "                   rho_e=0.95, sd_e=0.5, nE=5, amin=0., amax=50, nA=100) \n", "\n", "ss = hh.steady_state(calibration)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's plot consumption function of worker with average productivity in employment vs unemployment."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.plot(ss.internals['household']['a_grid'], ss.internals['household']['c'][0, 3, :], label='employed')\n", "plt.plot(ss.internals['household']['a_grid'], ss.internals['household']['c'][1, 3, :], label='unemployed')\n", "plt.xlabel('Assets')\n", "plt.ylabel('Consumption')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Consider the impulse responses of consumption end employment to a persistent rise in the separation rate."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["td = hh.impulse_linear(ss, {'s': 0.6 ** np.arange(50)})"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 576x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["fig, axes = plt.subplots(1, 2, figsize=(8, 4))\n", "ax = axes.flatten()\n", "\n", "ax[0].plot(td['s'], color='black', label='separation rate')\n", "ax[0].axhline(0, color='gray', linestyle=':')\n", "ax[0].set_title('Shock')\n", "ax[0].set_ylabel('deviation from ss')\n", "ax[0].legend(frameon=False)\n", "\n", "ax[1].plot(td['C'], label='consumption')\n", "ax[1].plot(td['U'], label='unemployment rate')\n", "ax[1].axhline(0, color='gray', linestyle=':')\n", "ax[1].legend(frameon=False)\n", "ax[1].set_title('Impulse Responses')\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"interpreter": {"hash": "df77b33bd3f53ce35917041dc902a6ccbdef1b9ffcd7a0c340a26560a2df0968"}, "kernelspec": {"display_name": "Python 3.8.3 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}