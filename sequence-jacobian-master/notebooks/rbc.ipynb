{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tutorial 1: An RBC model\n", "\n", "In this notebook we demonstrate the basic workflow of setting up and solving DSGE models in sequence space. All in the context of a simple RBC model.\n", "\n", "For more examples and information on the SSJ toolkit, please visit our [GitHub page](https://github.com/shade-econ/sequence-jacobian).\n", "\n", "1. Set up an RBC model\n", "2. Steady state\n", "3. Linearized impulse responses\n", "4. Nonlinear perfect foresight responses"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1 Set up an RBC model\n", "\n", "\n", "Let's consider a standard RBC model with the following equilibrium conditions\n", "\n", "$$\n", "\\textbf{F}_t(\\textbf{X}, Z) \\equiv \n", "\\begin{pmatrix}\n", "C_t^{-\\sigma} - \\beta (1 + r_{t+1}) C_{t+1}^{-\\sigma}\n", "\\\\\n", "w_t - \\varphi L_t^{\\nu} C_t^{\\sigma}\n", "\\\\\n", "K_t - (1 - \\delta) K_{t-1} - I_t\n", "\\\\\n", "r_t + \\delta -\\alpha Z_t \\left(\\frac{K_{t-1}}{L_t} \\right)^{\\alpha-1}\n", "\\\\\n", "w_t - (1-\\alpha) Z_t \\left(\\frac{K_{t-1}}{L_t} \\right)^{\\alpha}\n", "\\\\\n", "Y_t - Z_t K_{t-1}^\\alpha L_t^{1-\\alpha}\n", "\\\\\n", "Y_t - C_t - I_t\n", "\\end{pmatrix}\n", "= \\begin{pmatrix} 0 \\\\ 0 \\\\ 0 \\\\ 0 \\\\ 0 \\\\ 0 \\\\ 0\\end{pmatrix},\n", "\\qquad t = 0, 1, \\dots \n", "\\tag{1}\n", "$$\n", "where $\\textbf{X} = \\{C, I, K, L, Y, r, w\\}$ denotes the set of endogenous variables, and $Z$ is the only exogenous variable. As usual, labor market clearing is imposed implicitly and asset market clearing is omitted by <PERSON><PERSON><PERSON>'s law."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.1 Directed Acyclic Graph (DAG) representation\n", "\n", "The first step of solving the model is to come up with a Directed Acyclic Graph (DAG) representation for it and specify its blocks.\n", "\n", "These blocks are collections of the model's equilibrium conditions, typically written in terms of the conceptual components of the model --- firm, household, market clearing conditions --- where each block takes a set of parameters and variables as inputs and produces another set as outputs. Formally, a block is a mapping between two sets of real sequences. \n", "\n", "<!-- Let $\\mathbf{X} \\equiv (x_t)_{t \\in \\mathbb{N}}$ denote a real sequence, and $\\mathcal{I}$ and $\\mathcal{O}$ denote the (finite) index sets of inputs and outputs. So, a block is a function $h: \\{\\mathbf{X}^i\\}_{i \\in \\mathcal{I}} \\to \\{\\mathbf{X}^o\\}_{o \\in \\mathcal{O}}.$ -->\n", "\n", "The DAG serves as the organizing framework for these blocks, which reduce the total set of variables and equations from those in the equilibrium conditions to the minimum number of target equations and associated unknown variables/parameters needed to represent the same system. The rest of the variables/equations in the system can then be written as explicit functions of these unknowns and collected into blocks."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### The RBC DAG\n", "The 7 equations of the RBC model can be organized as three blocks which form a DAG in two unknowns $U = \\{K, L\\}$ and two targets, the <PERSON><PERSON><PERSON> equation and goods market clearing. We write the reduced system as\n", "$$\n", "\\textbf{H}_t(\\textbf{U}, Z) = 0\n", "$$\n", "Visual representation of DAGs is also very informative. We'll provide one for this model below."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Constructing a valid DAG\n", "- Valid unknowns for a DAG are any variables/parameters that are inputs to a subset of the blocks but are not outputs.\n", "- Valid targets are variables (or implicit equations) that are outputs of a subset of the blocks but not inputs.\n", "- The only other requirement is that each unknown must map to at least one target and each target likewise must be mapped to by at minimum one unknown."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### What makes a good DAG?\n", "\n", "Most macro models admit many different DAG representations.\n", "- a good DAG minimizes the number of unknowns\n", "- a good DAG uses economically meaningful blocks that can easily be deployed in different models \n", "\n", "Coming up with a good DAG is an intuitive process that gets easier with practice. \n", "\n", "**Note**: a bad DAG still delivers the same answers as a good one, albeit somewhat slower and less elegantly."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.2 Representing our RBC model\n", "\n", "SSJ has several subclasses of the abstract **Block** class.\n", "- `SimpleBlock` for explicit aggregate equilibrium conditions\n", "- `SolvedBlock` for implicit aggregate equilibrium conditions\n", "- `HetBlock` for collective actions of heterogeneous agents\n", "- `CombinedBlock` for combination of multiple blocks\n", "\n", "As we will see, blocks have a unified interface. By mixing these types blocks, we can represent most DSGE models. To represent our RBC model, we only need 3 simple blocks though."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from sequence_jacobian import simple, combine, create_model\n", "\n", "@simple\n", "def firm(K, L, Z, alpha, delta):\n", "    r = alpha * Z * (K(-1) / L) ** (alpha-1) - delta\n", "    w = (1 - alpha) * Z * (K(-1) / L) ** alpha\n", "    Y = Z * K(-1) ** alpha * L ** (1 - alpha)\n", "    return r, w, Y\n", "\n", "@simple\n", "def household(K, L, w, eis, frisch, vphi, delta):\n", "    C = (w / vphi / L ** (1 / frisch)) ** eis\n", "    I = K - (1 - delta) * K(-1)\n", "    return <PERSON>, I\n", "\n", "@simple\n", "def mkt_clearing(r, C, Y, I, K, L, w, eis, beta):\n", "    goods_mkt = Y - C - I\n", "    euler = C ** (-1 / eis) - beta * (1 + r(+1)) * C(+1) ** (-1 / eis)\n", "    walras = C + K - (1 + r) * K(-1) - w * L\n", "    return goods_mkt, euler, walras"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- SimpleBlocks are initialized as regular Python functions, endowed with the decorator `@simple`. \n", "- Inside the function, we can refer to **leads/lags** of any inputs. The convention is as follows: \n", "   - `Y` refers to $Y_t$\n", "   - `Y(-k)` refers to $Y_{t-k}$\n", "   - `Y(k)` refers to $\\mathbb{E}_t[Y_{t+k}]$\n", "- We cannot ask for leads/lags of variables in the same SimpleBlock in which they were created. For example, we could not add the line `Y_lag = Y(-1)` to the firm block above. If we really wanted it, we could create a new SimpleBlock that takes `Y` as input and returns `Y_lag = Y(-1)`.\n", "- Use SimpleBlocks only with **univariate** inputs and outputs. This is not a strict rule, but deviating from it imposes somewhat subtle restrictions on the model that we won't discuss here.   \n", "- Of course, we have to import the constructor ``simple`` from the SSJ package before we can use it. For convenience, such commonly used functions can be imported from the top level `sequence_jacobian`, without having to remember their precise location.  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Recall that every block (including simple blocks) represents a function in sequence space."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<SimpleBlock 'firm'>\n", "Inputs: ['K', 'L', 'Z', 'alpha', 'delta']\n", "Outputs: ['r', 'w', 'Y']\n"]}], "source": ["print(firm)\n", "print(f\"Inputs: {firm.inputs}\")\n", "print(f\"Outputs: {firm.outputs}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we combine the 3 simple blocks to get the RBC model. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Model 'RBC'>\n", "Blocks: [<SimpleBlock 'firm'>, <SimpleBlock 'household'>, <SimpleBlock 'mkt_clearing'>]\n"]}], "source": ["rbc = create_model([household, firm, mkt_clearing], name=\"RBC\")\n", "\n", "print(rbc)\n", "print(f\"Blocks: {rbc.blocks}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- The constructor `create_model` takes a list of blocks and (optionally) a name for the model.\n", "- The order of blocks in the input list does not matter. When a `Model` is created, the constituent blocks are sorted in the correct order of evaluation based on their inputs and outputs. This is called a topological sort. The SSJ toolkit represents models just like we draw them, as a directed acyclic graph (DAG).\n", "- We use the name `Model` for complete macro models such as `rbc`. A model is a special case of a `CombinedBlock` which can be any collection of blocks. The corresponding constructors---`create_model` for models, and `combine` for combined blocks---work identically.\n", "- SSJ supports nesting combined blocks. For example, we could define a combined block that represents the private sector and combine that with market clearing to form the model."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Model 'Nested RBC'>\n", "Blocks of RBC: [<CombinedBlock 'Private sector'>, <SimpleBlock 'mkt_clearing'>]\n", "Blocks of Private sector: [<SimpleBlock 'firm'>, <SimpleBlock 'household'>]\n"]}], "source": ["from sequence_jacobian import combine\n", "\n", "private = combine([firm, household], name='Private sector')\n", "rbc2 = create_model([private, mkt_clearing], name='Nested RBC')\n", "\n", "print(rbc2)\n", "print(f\"Blocks of RBC: {rbc2.blocks}\")\n", "print(f\"Blocks of Private sector: {private.blocks}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1.3 Automated DAG visualization\n", "\n", "We can visualize DAGs automatically using the `utilities/drawdag` module. The `drawdag` function takes a combined block (e.g. `rbc`), and the names of exogenous variables (`inputs`), unknowns, and targets.\n", "\n", "This feature requires the [Graphviz](https://www.graphviz.org/) graph drawing software and the corresponding [Python package](https://pypi.org/project/graphviz/). Graphviz is not included in the Anaconda Python distribution, so you need to install it manually if you'd like to use it."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.40.1 (20161225.0304)\n", " -->\n", "<!-- Title: %3 Pages: 1 -->\n", "<svg width=\"225pt\" height=\"392pt\"\n", " viewBox=\"0.00 0.00 225.32 392.00\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 388)\">\n", "<title>%3</title>\n", "<polygon fill=\"#ffffff\" stroke=\"transparent\" points=\"-4,4 -4,-388 221.321,-388 221.321,4 -4,4\"/>\n", "<!-- exog -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>exog</title>\n", "<polygon fill=\"none\" stroke=\"#000000\" points=\"81.321,-384 5.321,-384 5.321,-348 81.321,-348 81.321,-384\"/>\n", "<text text-anchor=\"middle\" x=\"43.321\" y=\"-362.3\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">exogenous</text>\n", "</g>\n", "<!-- 0 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>0</title>\n", "<ellipse fill=\"none\" stroke=\"#000000\" cx=\"52.321\" cy=\"-279\" rx=\"38.9931\" ry=\"18\"/>\n", "<text text-anchor=\"middle\" x=\"52.321\" y=\"-275.3\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">firm [0]</text>\n", "</g>\n", "<!-- exog&#45;&gt;0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>exog&#45;&gt;0</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M45.1858,-347.9735C46.4047,-336.1918 48.0217,-320.5607 49.4081,-307.1581\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"52.911,-307.3105 50.4586,-297.0034 45.9481,-306.5901 52.911,-307.3105\"/>\n", "<text text-anchor=\"middle\" x=\"53.821\" y=\"-318.8\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">Z</text>\n", "</g>\n", "<!-- unknowns -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>unknowns</title>\n", "<polygon fill=\"none\" stroke=\"#000000\" points=\"173.321,-384 99.321,-384 99.321,-348 173.321,-348 173.321,-384\"/>\n", "<text text-anchor=\"middle\" x=\"136.321\" y=\"-362.3\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">unknowns</text>\n", "</g>\n", "<!-- unknowns&#45;&gt;0 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>unknowns&#45;&gt;0</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M111.5724,-347.9681C104.7196,-342.5139 97.4781,-336.2934 91.321,-330 83.721,-322.2317 76.253,-312.983 69.9188,-304.5082\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"72.714,-302.4009 63.9997,-296.375 67.0542,-306.52 72.714,-302.4009\"/>\n", "<text text-anchor=\"middle\" x=\"104.321\" y=\"-318.8\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">K, L</text>\n", "</g>\n", "<!-- 1 -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>1</title>\n", "<ellipse fill=\"none\" stroke=\"#000000\" cx=\"114.321\" cy=\"-192\" rx=\"61.1893\" ry=\"18\"/>\n", "<text text-anchor=\"middle\" x=\"114.321\" y=\"-188.3\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">household [1]</text>\n", "</g>\n", "<!-- unknowns&#45;&gt;1 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>unknowns&#45;&gt;1</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M134.0082,-347.7078C130.1691,-317.3436 122.4538,-256.3226 117.9051,-220.3464\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"121.3297,-219.529 116.6029,-210.0471 114.385,-220.4072 121.3297,-219.529\"/>\n", "<text text-anchor=\"middle\" x=\"140.321\" y=\"-275.3\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">K, L</text>\n", "</g>\n", "<!-- 2 -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>2</title>\n", "<ellipse fill=\"none\" stroke=\"#000000\" cx=\"114.321\" cy=\"-105\" rx=\"71.4873\" ry=\"18\"/>\n", "<text text-anchor=\"middle\" x=\"114.321\" y=\"-101.3\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">mkt_clearing [2]</text>\n", "</g>\n", "<!-- unknowns&#45;&gt;2 -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>unknowns&#45;&gt;2</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M148.2619,-347.9538C169.6377,-313.2369 209.9182,-235.1738 184.321,-174 176.7775,-155.972 161.9743,-140.265 147.9164,-128.4104\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"149.7762,-125.4152 139.786,-121.8873 145.3956,-130.8752 149.7762,-125.4152\"/>\n", "<text text-anchor=\"middle\" x=\"204.321\" y=\"-231.8\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">K, L</text>\n", "</g>\n", "<!-- targets -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>targets</title>\n", "<polygon fill=\"none\" stroke=\"#000000\" points=\"114.321,-36 64.5517,-18 114.321,0 164.0904,-18 114.321,-36\"/>\n", "<text text-anchor=\"middle\" x=\"114.321\" y=\"-14.3\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">targets</text>\n", "</g>\n", "<!-- 0&#45;&gt;1 -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>0&#45;&gt;1</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M64.5702,-261.8116C73.4546,-249.3448 85.6303,-232.2596 95.7126,-218.1119\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"98.7183,-219.9249 101.6716,-209.75 93.0178,-215.8624 98.7183,-219.9249\"/>\n", "<text text-anchor=\"middle\" x=\"91.821\" y=\"-231.8\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">w</text>\n", "</g>\n", "<!-- 0&#45;&gt;2 -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>0&#45;&gt;2</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M35.4485,-262.5902C15.9804,-241.5786 -11.3415,-204.2684 5.321,-174 17.7959,-151.3388 41.2018,-135.0804 63.1358,-124.0602\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"64.879,-127.1069 72.4098,-119.6545 61.8753,-120.7841 64.879,-127.1069\"/>\n", "<text text-anchor=\"middle\" x=\"24.821\" y=\"-188.3\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">r, Y, w</text>\n", "</g>\n", "<!-- 1&#45;&gt;2 -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>1&#45;&gt;2</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M114.321,-173.9735C114.321,-162.1918 114.321,-146.5607 114.321,-133.1581\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"117.8211,-133.0033 114.321,-123.0034 110.8211,-133.0034 117.8211,-133.0033\"/>\n", "<text text-anchor=\"middle\" x=\"125.321\" y=\"-144.8\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">C, I</text>\n", "</g>\n", "<!-- 2&#45;&gt;targets -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>2&#45;&gt;targets</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M99.6055,-87.3816C95.828,-81.8407 92.2801,-75.4703 90.321,-69 87.3746,-59.2685 90.7737,-49.0348 95.8528,-40.3099\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"98.8697,-42.0961 101.5582,-31.848 93.0657,-38.1827 98.8697,-42.0961\"/>\n", "<text text-anchor=\"middle\" x=\"104.321\" y=\"-57.8\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">euler</text>\n", "</g>\n", "<!-- 2&#45;&gt;targets -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>2&#45;&gt;targets</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M116.7486,-86.7185C117.3945,-81.0506 118.0011,-74.7765 118.321,-69 118.7417,-61.4056 118.4332,-53.1848 117.8364,-45.6066\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"121.3005,-45.0669 116.8467,-35.4536 114.3335,-45.7461 121.3005,-45.0669\"/>\n", "<text text-anchor=\"middle\" x=\"149.821\" y=\"-57.8\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">goods_mkt</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.dot.Digraph at 0x126bfb670>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sequence_jacobian import drawdag\n", "\n", "unknowns = ['K', 'L']\n", "targets = ['euler', 'goods_mkt']\n", "inputs = ['Z']\n", "\n", "drawdag(rbc, inputs, unknowns, targets)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With graphviz installed, the output of the cell above should look like this. "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Image\n", "\n", "Image(filename='dag/RBC.png') "]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Note**: In the above DAG, we omit the various other variables/parameters that are pre-specified so the figure does not become too cluttered. However, one could visualize other variables such as the capital depreciation rate, $\\delta = 0.025$ in our standard calibration, as being defined outside of the DAG --- they could be potential unknowns/inputs but in this configuration we simply fix their values."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2 Steady state\n", "\n", "The next step of solving a model is to compute its steady state. SSJ provides functionality for computing a model's steady state from its DAG representation. This can be very convenient, but is optional. One can always compute the steady state manually.\n", "\n", "**Note**: a more advanced usage of `steady_state` and `solve_steady_state` methods is covered in the one-asset HANK notebook.  "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<SteadyStateDict: ['L', 'Z', 'r', 'eis', 'frisch', 'delta', 'alpha', 'vphi', 'beta', 'K', 'w', 'Y', 'C', 'I', 'goods_mkt', 'euler', 'walras']>\n"]}], "source": ["calibration = {\"L\": 1., \"Z\": 1., \"r\": 0.01, \"eis\": 1., \"frisch\": 1., \"delta\": 0.025, \"alpha\": 0.11}\n", "unknowns_ss = {\"vphi\": 0.9, \"beta\": 0.99, \"K\": 2., \"Z\": 1.}\n", "targets_ss = {\"goods_mkt\": 0., \"r\": 0.01, \"euler\": 0., \"Y\": 1.}\n", "\n", "ss = rbc.solve_steady_state(calibration, unknowns_ss, targets_ss, solver=\"hybr\")\n", "\n", "print(ss)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- `solve_steady_state` is a method that all Blocks have. It takes a calibration dict, unknowns to solve for (name and initial guess), and targets (name and value) that should be satisfied. \n", "- The `solver` keyword argument specifies which root-finding algorithm will be used to solve for the steady state. Any of the generic root-finding algorithms listed in `scipy.optimize` can be used.\n", "- `solve_steady_state` returns a `SteadyStateDict` object, which is similar to Python dictionaries with some helpful extra functionality.\n", "- To evaluate a block with constant inputs (without internal calibration), use the `steady_state` method, which takes only a calibration."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can inspect the values contained in the returned `SteadyStateDict` object below to confirm that the targets are indeed satisfied. As an additional check we verify that <PERSON><PERSON><PERSON>'s law is satisfied by including the resource constraint as an additional variable in the DAG."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Euler equation: 1.1102230246251565e-15\n", "Goods market clearing: 2.220446049250313e-16\n", "Walras law: -4.440892098500626e-16\n"]}], "source": ["print(f\"Euler equation: {ss['euler']}\")\n", "print(f\"Goods market clearing: {ss['goods_mkt']}\")\n", "print(f\"Walras law: {ss['walras']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 Linearized solution\n", "\n", "The linearized impulse responses of the model are fully characterized by the general equilibrium Jacobians $G = H_U^{-1}H_Z$ These matrices map *any* sequence of shocks into an impulse response, e.g. $dC = G^{C,Z} dZ.$ Once we have them, we're pretty much done!\n", "\n", "We can get all of these in a single call to the `solve_jacobian` method of the `rbc` object. This function takes in the `SteadyStateDict` we obtained from calling `rbc.solve_steady_state`, the names of exogenous shocks, the names of unknown endogenous variables, the names of target equations, and the truncation horizon."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<JacobianDict outputs=['K', 'L', 'C', 'I', 'r', 'w', 'Y', 'goods_mkt', 'euler', 'walras'], inputs=['Z']>\n"]}], "source": ["G = rbc.solve_jacobian(ss, unknowns, targets, inputs, T=300)\n", "\n", "print(G)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To see the $G$ matrices in action, let's construct two sequences of TFP shocks. First, a usual autoregressive shock that hits in period 0 (when the economy is assumed to be in steady state). Second, a news shock that, as agents learn in period 0, will hit TFP in period 10 and then decay at a constant rate. Note that such a news shock would be very costly to formulate recursively.\n", "\n", "We store the two sequences as columns of a matrix $dZ.$ Let the shock on impact be 1% of steady state productivity, and the  quarterly autocorrelation be 0.8. "]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["T, impact, rho, news = 300, 0.01, 0.8, 10\n", "dZ = np.empty((T, 2))\n", "dZ[:, 0] = impact * ss['Z'] * rho**np.arange(T)\n", "dZ[:, 1] = np.concatenate((np.zeros(news), dZ[:-news, 0])) \n", "\n", "plt.plot(100*dZ[:50, 0]/ss['Z'], label='regular shock', linewidth=2.5)\n", "plt.plot(100*dZ[:50, 1]/ss['Z'], label='news shock', linewidth=2.5)\n", "plt.title(r'Two TFP shocks')\n", "plt.ylabel(r'% deviation from ss')\n", "plt.xlabel(r'quarters')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Just apply the $G$ matrix to map these into impulses responses for, say, consumption. We multiply by 100 and divide by $C_{ss}$ just to get the answer in units of % deviations from steady state."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["dC = 100 * G['C']['Z'] @ dZ / ss['C']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now plot the result."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["plt.plot(dC[:50, 0], label='regular shock', linewidth=2.5)\n", "plt.plot(dC[:50, 1], label='news shock', linewidth=2.5)\n", "plt.legend()\n", "plt.title(r'Consumption response to TFP shocks')\n", "plt.ylabel(r'% deviation from ss')\n", "plt.xlabel(r'quarters')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For those of you familiar with <PERSON><PERSON><PERSON>, these impulse responses are identical to what you could obtain by running the perfect foresight solver `simul` with the `linear_approximation` option (which are, of course, also identical to those obtained by `stoch_simul(order=1)`)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that we can also perform the same calculation as the one above using the `solve_impulse_linear` method of the `rbc` object. This method is somewhat faster, because it only has to keep track of the responses to a specific shock (a vector) instead of the responses to all possible shocks (a matrix). "]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["regular_Z_shock_path = {\"Z\": dZ[:, 0]}\n", "\n", "td_lin = rbc.solve_impulse_linear(ss, unknowns, targets, regular_Z_shock_path)\n", "td_lin_scaled = 100 * td_lin / ss  # scale all variables at once\n", "dC_alt = td_lin_scaled['C']\n", "\n", "plt.plot(dC_alt[:50], label='regular shock', linewidth=2.5)\n", "plt.title(r'Consumption response to TFP shocks')\n", "plt.ylabel(r'% deviation from ss')\n", "plt.xlabel(r'quarters')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4 Nonlinear solution\n", "\n", "To obtain nonlinear impulse responses that capture the different scale and sign effects of shocks, we use the `solve_impulse_nonlinear` method. Similarly to `solve_jacobian` above, it takes in the `SteadyStateDict` object, the names of unknown endogenous variables and the names of target equations.\n", "\n", "However, the names of the exogenous variables would not be sufficient information, since we're calculating the nonlinear response to a specific shock path. Instead, `solve_impulse_nonlinear` requires the full *sequences* for any exogenous variables that are shocked.\n", "\n", "So for the news shock above, we can just call: "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solving RBC for ['K', 'L'] to hit ['euler', 'goods_mkt']\n", "On iteration 0\n", "   max error for euler is 1.04E-02\n", "   max error for goods_mkt is 7.86E-04\n", "On iteration 1\n", "   max error for euler is 6.68E-05\n", "   max error for goods_mkt is 6.75E-05\n", "On iteration 2\n", "   max error for euler is 3.85E-07\n", "   max error for goods_mkt is 1.27E-07\n", "On iteration 3\n", "   max error for euler is 3.60E-09\n", "   max error for goods_mkt is 1.47E-09\n"]}], "source": ["news_Z_shock_path = {\"Z\": dZ[:, 1]}\n", "td_nonlin = rbc.solve_impulse_nonlinear(ss, unknowns, targets, news_Z_shock_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As we can see, the linearized solution is very accurate in this case."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAYgAAAEWCAYAAAB8LwAVAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjUuMCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8/fFQqAAAACXBIWXMAAAsTAAALEwEAmpwYAABGaElEQVR4nO3dd3gVZfbA8e9JhyS0EFooCb2DNEFQQBRhVUTFrmtbkVXsDXcFy+pvbbtrL7hrV+wFFRULIChIF+mhBAgt1EBIT87vj5ngNabckNxMyvk8z30yd2beueede3PPnXdm3ldUFWOMMaawIK8DMMYYUzVZgjDGGFMkSxDGGGOKZAnCGGNMkSxBGGOMKZIlCGOMMUWyBGGOiYj8TUT+63UcxgSSiFwhIvMCsF0VkfYVvd2KZgmigojIxSKyWETSRGSniHwpIkO8jqsiiMgwEUn2naeq/6eqf/EqpppARGaLyDHvQxGZ6H7mskTk1ULLWonIAhHZLyL/KrTsKxHpV8w2W7uf4YKHisgRn+cnisirIpJdaL0L3PJJIpLhztstIq+ISNSx1tF4yxJEBRCRW4EngP8DmgKtgeeAszwMq0YQkRCvY6jCdgAPAi8Xsexu4DUgARhbkBDcL/JNqrq4qA2q6lZVjSp4uLN7+cyb68571Hc9VX3XZzNnumX7AP2Be8pdU+MJSxDlJCL1gQeA61X1I1U9oqo5qvqZqt7hrhMuIk+IyA738YSIhLvLholIsojcJiIp7tHHlT7b/5OIrBaRwyKyXURud+f/4dDX97DV/ZX3nHskkyYiP4pIM/e1D4jIWhE5zqdskojc7b7WAfeXX4SIRAJfAi18fi22EJH7RORNn/JjRGSViBx0fxl3KbTt20VkhYikisi7IhJRzP68wo31PyKyH7jP3X+Pi8hW91fpCyJSx12/sYh87r7ufhGZKyJBJdXJ57WuEZENbrnpItKi0L6cICKJbtlnRUTcZe1FZI5bl70i8q5Puc4i8o27zXUicn4x9XwIOBF4xt2nz7jzTxCRRe62F4nICUWVB3A/b58A+4pYnAB8r6qpwCKgrYjUAyYBfytumxVJVbfjfHa6F7W8tM+FiJwhIsvd9/YnEenpzr9SRD7zWW+DiLzn83ybiPQWx3/c/6tU93WKi+UKEdnk/p9tFpFLCi1/3P0cbBaR0T7zW7ifnf1uHNf4LAsWpyl2o7vdJSLSqojXHuLGPNyf/VqpVNUe5XgAo4BcIKSEdR4AFgBNgFjgJ+Af7rJhbvkHgFDgT0A60NBdvhM40Z1uCPRxp68A5hV6HQXau9OvAnuBvkAE8D2wGfgzEIzzy3OWT9kkYCXQCmgE/Ag86BNjcqHXug94053uCBwBTnXrcCewAQjz2fZCoIW77TXAhGL21RXu/rgBCAHq4BydTXfLRgOfAf901/8n8IL7uqE4X7riR51OdvdPHyAceBr4odC+/BxogHNEuAcY5S6bBvwd5wdWBDDEnR8JbAOudGPv475Gt2LqOhv4i8/zRsAB4DK3/EXu85hSPoMPAq8WmvcYMNGNfwPOl/STwOVl/Hwf/Uz5zHu1YD8WsX4ScIo73QpYhftZL2bdIj8X7r5LAY7H+bxe7q4fDrQFDrr7vzmwBdjulmvr7rMg4DRgibsPBOgCNC8ijkjgENDJfd684D3D+TzmANe4cfwV58it4DM2B6e1IALo7X5ORrjL7gB+BTq5r9+r4L0s2K9ujNuAAV5/lxX5HnkdQHV/AJcAu0pZZyPwJ5/npwFJ7vQwIAOfBOP+Ywx0p7cC1wL1Cm3zCkpPEC/5LLsBWOPzvAdw0Od5Ej5f2jiJaqNPjCUliMnAez7LgoDtwDCfbV/qs/xR4IVi9tUVwFaf54KTfNr5zBsEbHanHwA+pdCXmB91+h9OM0nBsij3iyDeZ18O8Vn+HjDJnX4dmAq0LPR6FwBzC817Ebi3mLrO5vcJ4jJgYaF15gNXlPL5KipBNALeBX4BbgGOA2a5898GfgAm+vH5Li5BZOJ8SR8E9hba52nu/C04X551itl2sZ8L4HkKJRZgHTDUnd6Gk0QudN+LhUBnnOQ83V3nZGA9MBAIKqGOkW685xaO1f08bvB5XtfdJ81wEmAeEO2z/J8F74Ub71kl7Ne73X3Uo7T3wauHNTGV3z6gsZTcVt4C54NQYIs77+g2VDXX53k6zhcWOB/aPwFb3GaNQWWIbbfPdEYRzwufPNxWQowl+V39VDXf3Vaczzq7fKZ961cU3zhicf4pl7hNDQeBr9z54PxS3gDMdJsIJpWwLd86FY45Dee99CfmO3ES10K3We0qd34b4PiCON1YL8H5MvFH4c9JQcxxRaxbIlXdr6oXqGovnCOHp3F+JEzCOao6BZggIl3Lum3X46rawH00LrRsrDu/japep6oZJWynuH3cBrit0L5sxW/v3xycHy4nudOzgaHuYw6Aqn4PPAM8C+wWkaluM9vvqOoRnOQ+AdgpIl+ISOeiYlTVdHcyyo1lv6oe9lnX9/1qhfPjsDg34/yw+rWEdTxlCaL85uP8mhpbwjo7cD7wBVq780qlqotU9Syc5qlPcH7JgvOrum7BeiLi75dQSXzbR31jLK3L39/Vz22rb4VzFHEsfF9vL04y6+bzhVRf3ROoqnpYVW9T1bbAmcCtIjLCp3xxdSoccyQQ40/MqrpLVa9R1RY4R3fPiXPuZxswxyfOBuqcwP2rH/X8Q0w+MR/rfiwwHligqitxjhwXq2o2TvNHkW3yVcA24KFC+7Kuqk5zlxckiBPd6TkUShAAqvqUqvYFuuE0hd5R1Iup6teqeipO89Ja4CU/YtwBNBKRaJ95vu/XNqBdCeXPw7mA4GY/XssTliDKSZ2TgFOAZ0VkrIjUFZFQERktIo+6q00D7hGRWBFp7K7/ZnHbLCAiYSJyiYjUV9UcnHbSPHfxL0A392RcBE6TT3ldLyItRaQRzonMgpOvu4EYcU7IF+U94HQRGSEiocBtQBbOuZZycY9GXgL+IyJNAEQkTkROc6fPcE8aC7/tnzyfTRRXp7eBK939F45zBdrPqppUWkwicp6ItHSfHsD5os/DOWfRUUQucz8DoSLSX3xO2BeyG6fNvMAMt/zFIhIizhVHXd3tFhVHiPveBwPB4lxUEFJonSbA9fz2+dgMDBfn0tN+wKbS6uuRl3COcI53TzZHisjpPl/Gc4DhOE1CycBcnPOBMcAyAHffH+9+Jo/g/JDLK/xCItJUnIssInE+t2lFrVeYqm7D+Yz/0933PYGrgbfcVf4L/ENEOrh16CkiMT6b2AGMAG4UkevKtHcqiSWICqCq/wZuxbmcbw/OL4eJOL/4wWkjXgyswPnVttSd54/LgCQROYRzCHyp+5rrcdrfvwUSgYq4medtYCbOl8amghhVdS1OktvkHu7/rulJVde5cT2N84v/TJxLHbMrICaAu3CakRa4++FbnBN/AB3c52k4R3PPqepsP+r0Hc65kw9xLgRoh9Oe7Y/+wM8ikoZz8vwmVd3sNjWMdLezA6dp4hGcE6tFeRIY514d85Sq7gPOwEmw+3Cass5Q1b3FlL8H5+hqEs7+z+CPl5Q+DjzgNqGB00Z+Ms5ndLoWc7mr19y4rsFpIjqA8/5f4bN8Pc57Ptd9fgjn/f1RVQu+3OvhJJoDOE0/+3D2R2FBOPt8B7Af5yjE3y/si4B4t+zHOOebvnGX/Rvnx9NMnB8v/8O56MK3nltxksRdUo57YgKl4Ey8qeVEJAnnhOm3XsdSUWpinYypTHYEYYwxpkiWIIwxxhTJmpiMMcYUyY4gjDHGFKlGdYTWuHFjjY+P9zoMY4ypNpYsWbJXVWOLWlajEkR8fDyLF1fJq/aMMaZKEpHCd+8fFdAmJhEZJU6PlhuK6AIBETlLnB4Wl4vTr/0Qf8saY4wJrIAlCBEJxukDZTTO3aAXFdHvy3c4fc33Bq7CufPQ37LGGGMCKJBHEANwekHc5N5R+w6FBtBR1TT97TKqSH7rm6bUssYYYwIrkOcg4vh9T5rJOH27/46InI1z+38T4PSylDXG1Hw5OTkkJyeTmZnpdSjVWkREBC1btiQ0NNTvMoFMEFLEvD/cdKGqHwMfi8hJwD9wuiH2qyyAiIzH6a2S1q1bH3OwxpiqKTk5mejoaOLj43H6ZDRlpars27eP5ORkEhIS/C4XyCamZH7f1XJLSujiWlV/ANq5vZ36XVZVp6pqP1XtFxtb5JVaxphqLDMzk5iYGEsO5SAixMTElPkoLJAJYhHQQUQSRCQMp4fL6b4r+HTTjIj0AcJwelwstawxpvaw5FB+x7IPA9bEpKq5IjIR+Bqnv/qXVXWViExwl7+AM1ran0UkB6er4gvck9ZFlg1UrKaC5eWQvf47Un56k3o7f+RIZGvqj5xE3a6jwP7Rjak2AnqjnKrOwBkExXfeCz7Tj+D0l+9XWVOF5eeTv/VnUn56g+iNnxOZl0rBiDr1UvfD+xeyK6orEX+ZQYMGDT0N1ZiyioqKIi0tjR07dnDjjTfywQcfeB1SpbC+mEy5Hc7M4fWPPiXo1VE0W/8WkXmpAGRoGLPyj+OwOmOkrE0NZfC/f+aRr9ayLy3Ly5CNOSYtWrQIeHLIzc0tfaVKYgnClEt+vvKX1xYzZXEoSflNydUg5uT34uXYScw96ycGTf6OJWf/wDt1LuKJ3HM5kp3H87M3MuSRWTwx4xfyjuz3ugrG+C0pKYnu3Z1hvF999VXOOeccRo0aRYcOHbjzzjuPrjdz5kwGDRpEnz59OO+880hLcwb0e+CBB+jfvz/du3dn/PjxFNwGNmzYMP72t78xdOhQnnzyycqvWDFqVF9MpvK9vXArP2/eDwhPNbiTE/r2YUS/7gyNDDu6zrDeHdFez9M8cS9Pf5fI4i0HCM05xAkLprB9ZTCtbpqJhEcX/yLGuO7/bBWrdxyq8O12bVGPe8/sVuZyy5cvZ9myZYSHh9OpUyduuOEG6tSpw4MPPsi3335LZGQkjzzyCP/+97+ZMmUKEydOZMqUKQBcdtllfP7555x55pkAHDx4kDlz5lRovcrLEoQ5Zilb1/LIl5sBiGtQh39cfwWR4UV/pESEoR1jOalDY+Zv2sfmd+9mQPY6SIek584l/obPIKS4oZuNcazeccj9QVI1jBgxgvr16wPQtWtXtmzZwsGDB1m9ejWDBw8GIDs7m0GDBgEwa9YsHn30UdLT09m/fz/dunU7miAuuOACbypRAksQ5phoZiohr53By9qQSXINk88eW2xy8CUinNCuMe3/+i8WPJ3IwPxlxKf+zKapl9J2wjsQFFwJ0ZvqqmuLelVqu+Hhv/2oCQ4OJjc3F1Xl1FNPZdq0ab9bNzMzk+uuu47FixfTqlUr7rvvvt/dlxAZGXlswQeQJQhzTJLevZOEvD00CtrDtW12M6xTkzKVb9KwHpnj32fFi6fTU9fRNmUmG1/7K+2ueNEuhTXFOpZmoMo2cOBArr/+ejZs2ED79u1JT08nOTmZJk2c/5HGjRuTlpbGBx98wLhx4zyOtmR2ktqU2eG1s0jY/A4Ai+jOqZfeWUqJorVuFkv4n98n0b1pvt2Wd9n43t8qLE5jvBAbG8urr77KRRddRM+ePRk4cCBr166lQYMGXHPNNfTo0YOxY8fSv39/r0MtVY0ak7pfv35qAwYFWE4Gex7tS2zOdjI0jB9HfsYpgweWa5O/rF5DzLtn0lL2ALC532QSzri9IqI1NcCaNWvo0qWL12HUCEXtSxFZoqr9ilrfjiBMmWz9aDKxOdsBmN7oKkacUP5Odnt17ULymdPYq047cJtFD7J51cJyb9cYUz6WIIzf0pMWEbfmfwCs0Pac9OfJFdZHzsB+/fl1+Cusz4/jypw7mfhdFjl5+RWybWPMsbEEYfy2//2bCCafbA1my5BHaN4wqkK3P3zYKXx6wgfMye/Fqh2HeOb7DRW6fWNM2ViCMH7J2LuFlkec/hK/jDqH00eMCMjr3HhKJzo3c26ae2bWBn7ddjAgr2OMKZ0lCOOX2TvDGJj5NJNzriB26HiCggJzKWp4SDD/Or8XIUHCAFYS9uoIMg/sDMhrGWNKZgnC+OXrVbvYRQwfBo+mz3F9A/pa3VrU5x/H5zEt7CE65W1gy+vXQg262s6Y6sIShClVTl4+361NAWBYp1giQgN/t/O4M05nXtgQADodmMPmWS8H/DWNCbTZs2dzxhlnADB9+nQefvhhjyMqmSUIU6rlvywjP/MwACO7NquU1wwNDqL5xc+xV51+bhr/MJmMfdsq5bWNqQxjxoxh0qRJAX2NvLy8cpW3BGFKVX/W3SwNv5bHQl9keOeydalRHu3i27Ckh9PzZTRHSH7tGmtqMp5ISkqiS5cuXHPNNXTr1o2RI0eSkZHB8uXLGThwID179uTss8/mwIEDgNN991133cWAAQPo2LEjc+fO/cM2X331VSZOnAjAFVdcwY033sgJJ5xA27ZtfzfmxGOPPUb//v3p2bMn995779H5Y8eOpW/fvnTr1o2pU6cenR8VFcWUKVM4/vjjmT9/frnqbX0xmRLlpx8k4fASQiWXmHqR1K8TWqmvf8o5VzNnwxcMzfyeDofms2Hmi7Q/bUKlxmCqmGVvwfK3S16nWQ8Y7dN8s3MFfHV30ev2vhiOu6TUl01MTGTatGm89NJLnH/++Xz44Yc8+uijPP300wwdOpQpU6Zw//3388QTTwDOwD8LFy5kxowZ3H///Xz77bclbn/nzp3MmzePtWvXMmbMGMaNG8fMmTNJTExk4cKFqCpjxozhhx9+4KSTTuLll1+mUaNGZGRk0L9/f84991xiYmI4cuQI3bt354EHHii1TqWxBGFKtGXhpyTgjHClnc+o9NcPDhLiL32G3S8NpqkcIGbB/5Ez5AJCI23Y0lrr4FbYMq9sZTJTiy8TP8SvTSQkJNC7d28A+vbty8aNGzl48CBDhw4F4PLLL+e88847uv4555xzdN2kpKRStz927FiCgoLo2rUru3fvBpyBh2bOnMlxxx0HQFpaGomJiZx00kk89dRTfPzxxwBs27aNxMREYmJiCA4O5txzz/WrTqWxBGFKlLliOgCHtA7dh5zpSQxtWsbxTbc7OXX13TTUVJZ/8BC9L3/ck1hMFdCgNbQp5Uu9WY/fP4+oX3yZBq39etnCXXsfPHjQr/ULugEvy/YL+shTVe6++26uvfba3607e/Zsvv32W+bPn0/dunUZNmzY0a7DIyIiCA6umAtJLEGYYmlOBm32O7+6fokYwIkNA9MXvz9OOns8C9e9y89Z8byx+US+TMsiJsoGGKqVjrvEryah32neE678okLDqF+/Pg0bNmTu3LmceOKJvPHGG0ePJirKaaedxuTJk7nkkkuIiopi+/bthIaGkpqaSsOGDalbty5r165lwYIFFfq6BSxBmGLtXP41LXB+lWS0G+1pLOGhIRw+913+9foSyIV/fbOe/zu7R+kFjQmg1157jQkTJpCenk7btm155ZVXKnT7I0eOZM2aNUdHpIuKiuLNN99k1KhRvPDCC/Ts2ZNOnToxcGD5elQujnX3bYq16oXL6bbrE7I0lB3jV5IQVzmXuBZHVbn8lUX8sH4PIvD5DUPo1qK+pzGZwLPuviuOdfdtKkZ+HnG7ZwGwLLSX58kBnOFKp5zRhZAgoaEeYt1bd6K5WV6HZUyNZQnCFGnXzu2szG1JrgZxsPVIr8M5qn2TaO7slcXs8Fs558g7rPnkMa9DMqbGsgRhivT1lnwuzfk7fbNeoOWJl3odzu9ccPpp7BPnMtc2K58hY7915lfT1aSmcK8cyz4MaIIQkVEisk5ENojIH+4pF5FLRGSF+/hJRHr5LEsSkV9FZLmI2ImFSvb1ql0A1K3fmG7xLTyO5vfqR9VlS/97AIgkg43vHtuY2KZ6iIiIYN++fZYkykFV2bdvHxEREWUqF7CrmEQkGHgWOBVIBhaJyHRVXe2z2mZgqKoeEJHRwFTAdwzL4aq6N1AxmqIdTM/m5837ARjZtWmFjRpXkU4cfRE/L3+Z43MW0XXXZ6Ssm0+TToO8DssEQMuWLUlOTmbPnj1eh1KtRURE0LJlyzKVCeRlrgOADaq6CUBE3gHOAo4mCFX9yWf9BUDZojcBse7bVxkn6/mWPpzWzfuT00UJDhIizniE7I9OJUzyOPzx7TS5ax5UwWRmyic0NJSEhASvw6iVAtnEFAf4dr+Z7M4rztXAlz7PFZgpIktEZHxxhURkvIgsFpHF9gujYrRY+QKPhL7EWxGP0D+hkdfhFKtXr7780GgcAO0yV7L5xw9KKWGMKYtAJoiifsoV2YgoIsNxEsRdPrMHq2ofYDRwvYicVFRZVZ2qqv1UtV9sbGx5Y671Mvdto1X2RgC2NB5GaHDVvo6h83n3kaqRAITOfgDNy/E4ImNqjkD+9ycDrXyetwR2FF5JRHoC/wXOUtV9BfNVdYf7NwX4GKfJygRY8urfbtmP7jLcw0j807JFCxbGXQ7A3uxQfv51nccRGVNzBDJBLAI6iEiCiIQBFwLTfVcQkdbAR8BlqrreZ36kiEQXTAMjgZUBjNW40rYuPzrdskv1yMl9zruLG/V2xmY/wANzDpCfb1e7GFMRApYgVDUXmAh8DawB3lPVVSIyQUQKOvSfAsQAzxW6nLUpME9EfgEWAl+o6leBitX8JjjFycPbNZaWzZp7HI1/Yho2oP1JFwLC6p2H+GzFHw5UjTHHIKCd9anqDGBGoXkv+Ez/BfhLEeU2Ab0KzzeBF5PmHMhtj2hPXFD1uSLo6iEJvD5/C3vTsnj867WM7tKYsHDr7dWY8qjaZyBNpcrLOESLPOfX95GG1atztMjwEG4a0Z7esoHHjvydddPuKr2QMaZEliDMUTvXLzk6HdKip4eRHJsL+7fiH3WmMTBoDR2S3iItZYvXIRlTrVmCMEdtOpDL53nHsym/GY079PU6nDILDQnm0JC/AxBBNps/mOxxRMZUb5YgzFHzM1sxMecmTs39Dwntu3kdzjEZNOwMfg5zrr7quns6+5N+9TgiY6ovSxDmqNU7DgHQtnEkEaEVM6ZtZQsKEsJG3keeCsGi7P74bq9DMqbasgRhjlq900kQXVt4N/Z0RTiu32DmRTljWHRJncuuVfM8jsiY6skShAFg3/ZNjE3/kCFBv9IztvoPVd5szH1kqVOPQ19M8TgaY6onSxAGgJRVc/h76Nu8GfZP+kRW/x7WO3Xqyo8NzwKgY/oSti35spQSxpjCLEEYADKSfwEgV4No1bmPx9FUjISxkzmkdZmWO5xnV1j3G8aUlSUIA0DEvlUAbJE4Gjeo73E0FSMhPoHHunzA3bnX8M465ZdtB70OyZhqxRKEAaBpeiIAKXU7eBxJxRo/8jhCg50uQx6faT29GlMWliAMmQd2EqMHnOnGXT2OpmK1alSXiwe0BmBh4g6WL/3Z44iMqT4sQRi2r110dDqiVW/vAgmQ609uz7iwn5gTfgtNv7gCzc32OiRjqgVLEIbDW5YdnW7esb+HkQRGk+gIRiWE0UwO0DxvB+u+fqH0QsYYSxAGZLfTHUWKNqRVqzYeRxMY/c69lZ3EABCz+EnyszM8jsiYqq/UBOGO7hbkTncUkTEiEhr40ExlWZXTgsX5HUkM70ZwNRoDoiwa1ItmVYfrAIjVvaz+7AlvAzKmGvDnCOIHIEJE4oDvgCuBVwMZlKk8+fnKQ4f+xLjs+/iiy8NehxNQA8+ZSBItAIhb+Ty56akeR2RM1eZPghBVTQfOAZ5W1bOBmnWpSy227UA6R7LzAOjavHr3wVSaqDoRbOp+EwANNZU1nzzqcUTGVG1+JQgRGQRcAnzhzqv+nfUY4LceXAG61PAEAXDCmKtZLwkAJKx/mcxD1b9bEWMCxZ8EcTNwN/Cxqq4SkbbArIBGZSpN1qrPGRP0E+2CdtC5WbTX4QRcRFgoO/veDkAU6az65N8eR2RM1VXqkYCqzgHmALgnq/eq6o2BDsxUjq6bX2Ns2K+sCepAZPg1XodTKQaPuog5y97k28xOfJM0hG+zcokKt4NiYwrz5yqmt0WknohEAquBdSJyR+BDMwGnSousjQDsi+7kcTCVJyQkmENjXuaNvJHsSldembfZ65CMqZL8aWLqqqqHgLHADKA1cFkggzKVI3XHBqJIByAvtnoOMXqsTu/R/Og5l6k/bOJgut1dbUxh/iSIUPe+h7HAp6qaA1jfyTXAznULj05HxdeMLr79FRQk3HFaRwAaZm9n/vv/8jgiY6oefxLEi0ASEAn8ICJtgEMlljDVQvq2X45Ot+rcz8NIvDG8UxPubLKQ78Ju57RNj7Bv03KvQzKmSik1QajqU6oap6p/UlUFtgLDAx+aCbTQvc4YEFtpRmxMjMfRVD4RYfDw0wkinyBRdn862euQjKlSytwXkzpy/VlXREaJyDoR2SAik4pYfomIrHAfP4lIL3/LmvKLPbIegF112iNSM7vYKE2v4wbwY9RIALqm/sCu1T95HJExVUfAOusTkWDgWWA0zp3XF4lI4TuwNwNDVbUn8A9gahnKmnLISttPs/wUADIa1e5d2+SMe8nWYABSv7jH42iMqToC2ZvrAGCDqm5S1WzgHeAs3xVU9SdVd6QaWAC09LesKZ8tO3YxM68vydqYsJa9vQ7HU527dOPHBmMA6HRkCduWfOVxRMZUDaXeHeT+mj8diPddX1VLuwU1Dtjm8zwZOL6E9a8GvixrWREZD4wHaN26dSkhmQKrj9Tn5pzbAPiq9xCPo/Fe/NmTSX9lBnUli9yZU6DPaVBLm92MKeDPEcRnwBVADBDt8yhNUf9dRV4eKyLDcRLEXWUtq6pTVbWfqvaLjY31IywDkHwg/eh065hIDyOpGhLi2/FT7PnOdNY6Ns55y+OIjPGeP/0LtHTPEZRVMtDKdzvAjsIriUhP4L/AaFXdV5ay5thtP+gMmNOwbih1w6ybCYBu503mwHOf0FAOs+7nr2k79JJae/LeGPDvCOJLERl5DNteBHQQkQQRCQMuBKb7riAirYGPgMtUdX1ZypryabRjDoOCVtGz3hGvQ6kymjdtyqwOd3Ne1hSuO3Ah361J8TokYzzlT4JYAHwsIhkickhEDotIqTfKuZfCTgS+BtYA77m9wU4QkQnualNwmq6eE5HlIrK4pLJlrp0p1mX7nmJa2ENcl/uG16FUKSefcw3rwrsD8MhXa8nLt04DTO3lT9vCv4BBwK/ujXJ+U9UZOP03+c57wWf6L8Bf/C1rKobm5dI4fy8I5ES18DqcKqVB3TD+Oqw9j3y1lsSUND5cmsz5/VqVXtCYGsifI4hEYGVZk4Opug7s3kqI5AMgDezKr8KuHBxPs+hwTg5aSqcvxpGZusfrkIzxhD9HEDuB2SLyJZBVMNOPy1xNFbV/+0YaudPhMW08jaUqiggN5qF+aYyY/zgorPjgPnpe/azXYRlT6fw5gtgMfAeEUbbLXE0VlZby2/gH9ZsleBhJ1TV0xJksD3bORXTe9g6Hdm3yOCJjKp8/I8rdDyAi0c5TTQt4VCagcvZvOTrduGV7DyOpukJCgskcOhm+v4Awcln3wT30mPi212EZU6n8GVGuu4gsA1YCq0RkiYjUrtFlahhJTQbgoEbRoEFDj6Opuo4/8TTmh58AQLc9M9i7canHERlTufxpYpoK3KqqbVS1DXAb8FJgwzKBFH7EuedwT3ATuxGsBCJC5OgHyNUggkTZ+/HdXodkTKXyJ0FEquqsgieqOhtn8CBTTaXlBpGmERwOb+Z1KFVez979mRc9GoDOaQvYuuhzjyMypvL4kyA2ichkEYl3H/fgnLg21dSE3NvonvU/Pmn/D69DqRbajHuQNI1wnsy8B83zazgUY6o9fxLEVUAsTpcYHwGNgSsDGZQJnLSsXFIzcgChaaMGXodTLSTEt2VB8z+ToWF8nNGbOet2eh2SMZWixKuY3K6+31fVUyopHhNg2w9kHJ1u2bCOh5FUL8dd8HfOfKI3G3Ib0P6rTQzpFEdIcCCHUzHGeyV+wlU1D0gXkfqVFI8JsJRdySTITsLJJq6BJQh/xTRswLiTBwKwISWNdxZtK6WEMdWfP3dSZwK/isg3wNGuP1X1xoBFZQImYu1HzAp/FIDd4Svh6D3VpjRXnBDPG/O3sP1gBq/OXMTYDiFExcR5HZYxAeNPgvjCfZgaIP+g88s3U0Np3MS+3MoiIjSYSaM6sfr9B7gu71M2TxtOj4nTvA7LmIApNkGIyHeqOgLoqqp3FbeeqV7C0rYDkBIUS2trQy+zM3q1IPbLrURnZdBj7wx2rZ1Ps86DvA7LmIAo6RuiuYgMBcaIyHEi0sf3UVkBmooVnelcgXMwzO6BOBYiQtTpD5KjwQAc/vQusI6OTQ1VUoKYAkzCGe7z3zjjQhQ8Hg98aCYQGuXuBiCjTnOPI6m+uvfsy9yGZwPQIeMXNs17z+OIjAmMYhOEqn6gqqOBR1V1eKHHyZUYo6kg2RlHaIQzGGBuvZYeR1O9dTr/AQ6q06FAxOz70JxMjyMypuKV2gitqna7bQ2xZ/uGo9MhDW2goPKIaxHHojbjAWiRt4NVH/3T44iMqXh2lrIWObjjtzEN6sbGexdIDTHwgjvZhHMk1m7N86SlbCmlhDHViyWIWuTIvuSj0w1atPMwkpohOrIu2wfdD0Adsljy6dMeR2RMxfLnPoiCLjea+q6vqlsDFZQJjB+jTuPKzObEyV6mx7X1OpwaYcjIc/n8l+l8dbA1X20exFcph2nfxAZcNDWDPwMG3QDsBr7ht5vmrM/jamj7wQyOUIcDUe2JCA/3OpwaQUSIv+RpvtBB5ObDfdNXo3bZq6kh/GliugnopKrdVLWH++gZ6MBMxSvoqM/6YKpY3ePqc/EA56T/vA17+XrVLo8jMqZi+JMgtgGpgQ7EBN7egwcBiLNeXCvc7SM70aBuKL1kA40+uoCMA5YkTPXnzzmITcBsEfkCyCqYqar/DlhUpsLl5+byxZGLSQuvw5LMawC7Gb4iNYwM48GBcMZPUyAffp12Jz2ue93rsIwpF3+OILbinH8IA6J9HqYa2bd7K2GSRyNJIzqyrtfh1EijR5zCotB+AHTbPZ1dq370OCJjyqfUIwhVvR9ARKKdp5rm78ZFZBTwJBAM/FdVHy60vDPwCs7P2b+r6uM+y5KAw0AekKuq/fx9XfNH+7ZvItadjmjcxtNYaqrg4CAiznyUrA9HEi65ZEy/BbosgCC7mtxUT/5cxdRdRJYBK4FVIrJERLr5US4YeBYYDXQFLhKRroVW2w/cSPF9Ow1X1d6WHMrvSMpvw4jXa57gYSQ1W4+efZnX+AIAErLWsebzJz2OyJhj589Pm6nAraraRlXbALcBL/lRbgCwQVU3qWo28A5wlu8KqpqiqouAnDLGbcooZ99vd/nGxrX3MJKar8cl/2C7e7zWaumjHNlro8+Z6smfBBGpqrMKnqjqbCDSj3JxOFdAFUh25/lLgZnuEcv44lYSkfEislhEFu/Zs6cMm69d5JDzVqQSSXR9G0UukJo0iiGx730ARJHOljcnehuQMcfInwSxSUQmi0i8+7gH2FxqKZAi5pXlDqLBqtoHp4nqehE5qaiVVHWqqvZT1X6xsbFFrWKAiPQdAOwNauJxJLXDSadfwo8Rzke268HZbPrxQ48jMqbs/EkQVwGxwEfAx+70lX6USwZa+TxvCezwNzBV3eH+TXFfd4C/Zc0f1c9yrss/FGEDBVWGoCChxYVPcECjeCP3FO5cFEluXr7XYRlTJv50931AVW9U1T6qepyq3qSqB/zY9iKgg4gkiEgYcCEw3Z+gRCTSvWoKEYkERuKcJDfHQPPzic1LASAr0sahriwJ8e2YdvwnTM69isW78nj5R38OvI2pOkoak/oJVb1ZRD6jiKYhVR1T0oZVNVdEJgJf41zm+rKqrhKRCe7yF0SkGbAYqAfki8jNOFc8NQY+FpGCGN9W1a+OpYIGUjNyODf7H8TJXsYkHOd1OLXKVaf24YPVc9m09wj/+SaR0d2b06qR3YdiqoeS7oN4w/17zMOLquoMYEaheS/4TO8Cihra7BDQ61hf1/ze9tRMNmocGzWOC1r29jqcWiUiNJj/O6cHF05dQHDOYRa89jda3vgYEhzqdWjGlKqkIUeXuJO9VXWO7wPoXSnRmQpR0EkfWD9MXhjYNoYbuufwTfidnJf6Cqs/fsTrkIzxiz8nqS8vYt4VFRyHCaDtB30ShPXk6omrx5xMlkQA0G7lkxzasd7jiIwpXbEJQkQucs8/JIjIdJ/HLGBf5YVoyqvdyqf5MmwSL4Q9SeOoMK/DqZUa1KtH8hBn3OoIskl54y+Qb1c1maqtpHMQPwE7cU4Y/8tn/mFgRSCDMhUr8lAiXYK2EiW5uCf+jQcGjziL738Zw8mHp9M+4xfWfPooXc6e5HVYxhSrpHMQW1R1tqoOKnQOYqmq5lZmkKZ8otx7IFLDmnocSe0mIvS4/Am24bwPCb/8iwNbV3kclTHF86ezvoEiskhE0kQkW0TyRORQZQRnKkZMrnMPREbdFh5HYmIbx7DlxMfJVyGCbA68dTWaZ12RmarJn5PUzwAXAYlAHeAvwNOBDMpUnIz0IzTmIAB50UVdUWwq2+CTz+T7huMAaJu1htUfPOhxRMYUza+O6lV1AxCsqnmq+gowPLBhmYqSkrzx6HRwo9YeRmIKiAjHXf44m4ljbX4r7l/TnN2HMr0Oy5g/8CdBpLtdZSwXkUdF5Bb8683VVAEHd206Oh3ZJN67QMzvxDRswLY/vcaY7AdZmNmKuz5cgWpZ+rI0JvD8SRCX4XSVMRE4gtMB37mBDMpUnMw9SUenGzRv510g5g9OGtCfM46LB2D2uj28u8jGjTBViz+d9W1R1QxVPaSq96vqrW6Tk6kGklJSAchXITbORpKrau49sxvN6kUAytLPX2TXmp+8DsmYo0q6Ue499++vIrKi8KPyQjTHatrCrdy1pR+3ZP+VNRE9CQ2L8DokU0j9uqE8fE53ng59mkeDnkE/uJrc9FSvwzIGKPkI4ib37xnAmUU8TBW2ZMt+pnzq9JA+t+4pNJowo5QSxivDOjclzO1EsXneDta9fK23ARnjKulGuZ3u5DlArtvUdPRROeGZY5GyJ4U73viBnDwlJEh4/tI+NG8Y5XVYpgRDr3yQJSG9Aei290vWz5zqbUDG4N9J6no4Y0PPFZHrRcRux63CsnJy2PrSxbycfScdZRv3jelG/3gbg7qqiwgLpdGlL7NX6wPQ8qfJ7N+62uOoTG3nz0nq+1W1G3A90AKYIyLfBjwyU2aqytypt9EvexHxQbt5tMk3XHK83ftQXSTEt2PV8Y8CUJdMDr9xKfnZdn+E8Y5fN8q5UoBdOD25NglMOKY85kx/hVP2vAZAcnBLuoz/r3XOV82cNPoCvm10EQBtcjay+vWbvQ3I1Gr+9MX0VxGZDXyH07PrNaraM9CBmbJZsexn+i29G4A06hBx2TuERzb0OCpTViLC8Vf/m1VBHQHonjyNxMXfeRyVqa38OYJoA9ysqt1U9V5VtYbRKmbd2pXEfnoRUeI0R+wa8RSN43t4HJU5VtGRdQka9z/2aj0m51zB1d8JhzKtQz9T+fw5BzEJiBKRKwFEJFZE7I6rKmLDhnXUfeccmrtjOP3a6Qban3i+x1GZ8urStSefDfuKN/JGsvVABpOsKw7jAX+amO4F7gLudmeFAm8GMijjnw0phznw5lW0YjcAq+Ivp8eF//A4KlNRLh/alWGdYgGY8esups5a63FEprbxp4npbGAMTj9MqOoOIDqQQZnSJe09wsUv/cwdWVeyXWNY1fICul3+JNhJ6RojKEh44oLetGpUh9aym6Gzz2PdjGe8DsvUIv4kiGx1jm0VQESsJ1ePbdufzsUvLSDlcBZJ2pyvT3iHble9YMmhBmpQN4ypl/bh5bDH6Ry0jYSF97Jz1VyvwzK1hD8J4j0ReRFoICLXAN8CLwU2LFOcXSkp/OvFF9mR6pyQvvHk9lx12gAIKssVy6Y66dKiAbtOepgcDSaMXEI+uJz0/du9DsvUAv6cpH4c+AD4EOgETFFVG1HOA5s2b2bvC6fzeOYDnBn0E9ee1JZbTu3odVimEgwZMYbv428BIFb3seOlC9DcLI+jMjWdvyPKfaOqd6jq7ar6TaCDMn+06Oe5hL96Kt3z1xMi+VwX+yuTRnWyG+FqkRGX/Y0f6o4EoH3Gr6x6ZaLHEZmarqTuvg+LyKHiHv5sXERGicg6EdkgIpOKWN5ZROaLSJaI3F6WsrWFqvLtJ6/RZcY44mQPAGubnE7nie8h1qxUq4SEBNPj2v+xRtoD0H37e6z/6nmPozI1WUm9uUaraj3gCWASEAe0xLnktdRR1kUkGHgWGA10BS4Ska6FVtsP3Ag8fgxla7yc3Dy+fOkeTl52E1GSSb4Ka7vfRue/voWE2tgOtVHD+vUIuuhN9mk9AOIX3EPyspkeR2VqKn9+gp6mqs+p6mF3VLnn8W/I0QHABlXdpKrZwDvAWb4rqGqKqi4CCt8mWmrZmi718BHm/edi/rTjGYJEySCcbadOpfO4KXa1Ui3XqWMX1pz4NNkazD6tx9+/3EbKYevUz1Q8fxJEnohcIiLBIhIkIpcAeX6UiwN8B9lNduf5w++yIjJeRBaLyOI9e/b4ufmqbVHSfl57ejLDj3wFwJ6gxqRf+gVthtgd0sYx5JSxzOz2KGdn3c+cQ824+tXFpGfneh2WqWH8SRAXA+cDu93Hee680hT1M9ffvgL8LquqU1W1n6r2i42N9XPzVdORrFzum76K81+cz1OHhjI3rztJ4Z2JmvgDMe37ex2eqWJOP+9qhvbrDcCv21O54e1l5OblexuUqVFCSltBVZM4tuadZKCVz/OWwI5KKFstLVm2mIe/SmRRqtO2HBwSxsbhzzF4cAeCwut6HJ2pikSEB8/uzo7UDOYm7iVk/eesefr/6H7D+0hwqf/axpQqkJfBLAI6iEiCiIQBFwLTK6FstXIoPZMvXvw7XT8ZzW0ZTyLk0z++IV/edCJXnNzLkoMpUWhwEM9d0oerGq3k+dAn6XHwe1a/PAGsYz9TAQKWIFQ1F5gIfA2sAd5T1VUiMkFEJgCISDMRSQZuBe4RkWQRqVdc2UDF6oWsnFxmfvEBWx8bwuk7n6GOZDMwaA3PD8nm3fGDaBtrY0gb/0RHhHLtFVexMcgZPbDb9vdZ8+FDHkdlagKpSV0I9+vXTxcvXux1GCXKzM5lzlfv0nTZ0/TWNUfn7wpuTtDYZ2nSY4SH0ZnqLDFxHdFvjqKZ7Adg/cBH6DhqgsdRmapORJaoar+ilvl9BCEiA0XkexH5UUTGVlh0tUR6di5ffvwmG/5vIKctve5ocsgkjLXtrqLpXUssOZhy6dChE9tPf53DWgeAdvMnsXHW6x5HZaqzYs9kiUgzVd3lM+tWnG6/BfgJ+CSwoVV/efnKoqT9fPnrTj5bsZMJWTMYHZIIQAYRbGl3Ce3OuovO9Zp6HKmpKfoOOJGfjvyP3rOvpK5k0Xr2zWwOq0vC4HFeh2aqoZIudXhBRJYAj6lqJnAQ5/LWfMCvrjZqo7x8ZdmqVexe8D7JO3fyz/TfLgB7idMZF/ojezpcSLsz76BzdGMPIzU11QnDT2du9nMM+GkC4ZJD3DfXsjmyMQm9h3kdmqlmik0QqjpWRM4EPheR14CbcRJEXWBspURXCbYfzCBxVyot6oXRvFE00RGhfpfNy1e2pBxg+4YVHN7yC5Kymhapy+jHOgDSNZz/MIqcoAhOaBfDBf2Po36X9TQKDQ9UdYwB4MTTxjErO4PBi29iXn537pmexust0mjfxC5+MP4r8WJpVf1MRGYA1wEfAQ+pao0arWTW2hSmfvods8NuJYUGbJLGpIY0IaNOM3KjW0BYFORmkp+Xy/cNxpGVm0dWTj6SupXb999HW7bTVoq+sfxISAP+c2I9jh90Eo0iwyq5Zqa2G37mZXxGJLf+GExOjnDJfxfw/rUn0DrGLp02/inpHMQY4E6cbjXuA94ApojIdcA9qrqxUiIMsB0HM2gh+wgSpRkHaMYByE2EwzgPV54KN2weSMFN3uEE8WL4NoLlt6vA8ghid1hrDrU6mbgTLiS27QBGW79JxkNnnnkOWyITeXzmenYfyuLSl37k3Qta0Dyhm9ehmWqgpCOIB4FBQB1ghqoOAG4VkQ7AQzg3r1V7Vw1J4LTmI1i3eh+amkxo2k4iM3fSMHcP4WQfXS9YlISG4YSEhhEWEkS9iFBWZP2JiHqx1G3VkyYd+lCneVdahITTwsP6GFPYxJM7kJ6dx3OzN/DXtGeJfu1ntp/zNnE9h3sdmqniSkoQqThJoA6QUjBTVROpIckBoHFUOI179YFefX6/QBXS90NOOoTWgZBwZoVFFepJdWClxmrMsbrjtE40S13GRWtmARD80QVsyXqFNv1P9zgyU5WVdB/E2TgnpHPxr3O+mkUEImOgQSuIbAzh0dbNtqm2RITLzr+QmR3uI0+FOmTR/Is/s2nuu16HZqqwkgYM2quqT6vqC6pql7UaU82JCCMvuYVZPR4hW4MJI5fW305g/beveB2aqaJszEpjaplTxl3LT/2fIlNDCZF82s+9hbVfPON1WKYKsgRhTC007IxLWTzkJdI0giBROi/6O6vef8DrsEwVYwnCmFpqyKlns+qUN0jVSACmLd/PU98lUpM68DTlY6OKGFOLHX/iSJbXeZ8fPn+dN/NOhW/Wk7TvCA+f05OwEPv9WNvZJ8CYWq53v8H86brHadXI6QX2o6XbueXF6RzaucHjyIzXLEEYY2jfJJqPrxvMca0bUI8j3LT7b+ROHcHO1T96HZrxkCUIYwzg3DQ67ZqB3NV6LR2DttNID9LgvbEkznrT69CMRyxBGGOOiggN5qIJk5kZf4d7Q102HeZcz4pXbkDzcrwOz1QySxDGmN8JChJGXnEPPw54jkPq9Pzac8vrbHj8ZI7s2+5xdKYyWYIwxhTppNMvJvn8L0mUNgB0yFhBxjNDSP7lO48jM5XFEoQxplhdu/Um5qYfmFv3FAAa635yP7qeL5Zv9TgyUxksQRhjStSoQQNOuO19vml3N4e0DtdnT+T6d37lvumryMwperAsUzNYgjDGlCo4OIhTL5vEL+fOIzmiIwCv/pTEWc/8yMaVCz2OzgSKJQhjjN9O7Nmez28YQq9WDQDosGcm7T44lRX/vY687AxvgzMVzhKEMaZMWjWqy4cTBnHryfH8PfQtAHomv8X2Rweye/0ij6MzFSmgCUJERonIOhHZICKTilguIvKUu3yFiPTxWZYkIr+KyHIRWRzIOI0xZRMSHMSNI7txYNyHrA7qAEDr3CQavjWKX997wO6ZqCECliBEJBh4FhgNdAUuEpGuhVYbDXRwH+OB5wstH66qvVW1X6DiNMYcu649+hB/x1y+a3oluRpEmOTSY/W/SHp4ELvWLvA6PFNOgTyCGABsUNVNqpoNvAOcVWids4DX1bEAaCAizQMYkzGmgtWtU4cRf32CZae+yxacf9+EnERip41i8Su3k5OX73GE5lgFMkHEAdt8nie78/xdR4GZIrJERMYX9yIiMl5EFovI4j179lRA2MaYY9F/yEjq3/Iz38f+mRwNJliUzzdkcebT81i+7aDX4ZljEMgEIUXMKzwSSUnrDFbVPjjNUNeLyElFvYiqTlXVfqraLzY29tijNcaUW4P69Tn5+qdZc9YXTA8dxet5I1m76zBnP/cj9366ktQD+70O0ZRBIBNEMtDK53lLYIe/66hqwd8U4GOcJitjTDXQs88gTrvrbW46pTNhwUGowtwF8wl6siu/vH4HORmHvA7R+CGQCWIR0EFEEkQkDLgQmF5onenAn92rmQYCqaq6U0QiRSQaQEQigZHAygDGaoypYOEhwdx0Sge+vPlEBrZtxN9C3iKaDHptmkrqoz1ZPeN5NN/uxK7KApYgVDUXmAh8DawB3lPVVSIyQUQmuKvNADYBG4CXgOvc+U2BeSLyC7AQ+EJVvwpUrMaYwGkXG8W0awYSffKtrA1qB0BjPUDXhZNI+ucAtiyd6XGEpjhSkwYo79evny5ebLdMGFNVZeXkMP+jZ+my5gmacuDo/NVRg2h4+r007zLIw+hqJxFZUtytBHYntTGm0oSHhjLsgpsJuXEps5pdSYaGAdA1bT5N3xnNP9/+km370z2O0hSwBGGMqXQxjRoxfMIT7PrzPObWO5McDear/P68uCKf4Y/P5u6PVpB8wBKF10K8DsAYU3sltOtEwq1vkrhuFXPnbYJEyM1Xpi3cRsbS9/hLoxXUP+VWWvUc5nWotZKdgzDGVBkrt6fyxLfrmbVmJzPD7qRd0E4AEsO7oYNupMNJ5yFBwR5HWbOUdA7CEoQxpsr5dcMWUj+5kwGHvyFMfrsUdntwHPt6XEPXUeMJiYj0MMKawxKEMaZaSkraQNKM/9Bn90fUk9/OSaQSxca4sbQ6awqxTZp6GGH1Z1cxGWOqpfj49gy77llyblrJrIRb2EljAOqTRuvk6Qx9YgF/fXMJ8xL3kp9fc37sVhV2BGGMqTYyMrNY8tWrRK98g58y43kk96KjywY3PMCtTZbSevjVxMZ38zDK6sWamIwxNYqqsnzLPt5atJ3PftlBVm4+k0Pe4OqQLwHYENaZwx3PpeOIy4lsaE1QJbEEYYypsVLTc/hwaTLdZl3N8XlLfrcsR4NZGz2QoN4X0nHIOYRGRHkUZdVlCcIYU+OpKutWLiXlx9dov2sGLfj9+DAZhPNR3B00G/JnhnRoTHiIXS4LJScIu1HOGFMjiAide/Slc4++5OT+myU/fkXmkrfpmfo90ZJBHbJ4e1MdVm1cTFR4CCd3bsJFzXfSo1cfoho28zr8KsmOIIwxNdqhw4dYPfcT0tbO4vr955GV63znBZHPwvDraMRhNoZ3JrXlMJr2HUPLLsfXqpvxrInJGGOAI1m5zF63hy9X7uTw2tm8FnT/H9bZR0O2NDqB0I4jiO8/iuiYwiMl1yyWIIwxppDMrCxW/zyTIyu/pMWeubTTrUWud1nMNLp3aMsJ7WLo16YRdcJq1tGFJQhjjCmBqrJl0zqSF35KnS3f0yVjGXUli/X5cYzMfuzoeg2DM3mvzsOkxvQiLOEEWvU+mYbN4r0LvALYSWpjjCmBiBDfrjPx7ToDd3HkyBGWLZ1F4rbd9NhXn5U7UlGF43Q1HXLXw+71sPt9WAC7JZYd9XqRH9efRh0H0apLf0LC63pdpQphRxDGGFOK1PQcFmzex76l0+m95WXa5SQSLrlFrntAo7i6ybv0bNWQni3r07NFFAkxdQgODa/kqP1jRxDGGFMO9euGclq3ZtBtPDCetCNprF0+j0OJ86i7ezEJ6StpJIcBWJvfmqXbUlm6LRWArpLEp2GT2RbSiv1RHclr0o3oNr1p0bk/9RpX7RPgliCMMaaMoiKj6DV4FAweBUBeXj6bN61m95r5bDyYT+/DDVi98xDZufl0ka2ESh7xeUnEpyZB6kxIBL6Fg0SzK6wNh+p1ZH3fe2nfNJoOTaKIiaoaRxvWxGSMMQGQk5fPul2H2bHmJyLXf0bUwTXEZW+kMal/WHdjfnNGZP/r6PMOEYd4MfgRUuu2Jqd+AiGN2xMd15nY1p2pHxuHBFVcR9zWxGSMMZUsNDiI7nH16R43Gk4ZDThXS+3asY2d6xeTvu0XgvclUv/IRtZlN/ld2djsrbQN2wyHN8PhOZAMLHeWZWgYKcFNSQ1vwfTOD9MipiGtGtWlW4t6tGhQp0LrYAnCGGMqiYjQLK41zeJaA+ccnd8pX+l7MIMNe9LYsDuN/C0H+CW5LzFZyTTXFILlt5aeOpJNm/xtHErfy3/n7wCcYVnvHNWJ64a1r9B4LUEYY4zHgoKEVo3q0qpRXYZ3agK0BS4FICMjg51b1nJg6xoy92xCDm4hIi2ZQzlBROQHk5mTD0CrhhV/aa0lCGOMqcLq1KlD287HQefj/rBsjSp70rLYtj+DhMYVP0a3JQhjjKmmRIQm0RE0iY4IyPYDOia1iIwSkXUiskFEJhWxXETkKXf5ChHp429ZY4wxgRWwBCEiwcCzwGigK3CRiHQttNpooIP7GA88X4ayxhhjAiiQRxADgA2quklVs4F3gLMKrXMW8Lo6FgANRKS5n2WNMcYEUCATRBywzed5sjvPn3X8KQuAiIwXkcUisnjPnj1FrWKMMeYYBDJBSBHzCt+2Xdw6/pR1ZqpOVdV+qtovNja2jCEaY4wpTiCvYkoGWvk8bwns8HOdMD/KGmOMCaBAHkEsAjqISIKIhAEXAtMLrTMd+LN7NdNAIFVVd/pZ1hhjTAAF7AhCVXNFZCLwNRAMvKyqq0Rkgrv8BWAG8CdgA5AOXFlS2dJec8mSJXtFZMsxhtwY2HuMZaszq3ftYvWuXfypd5viFtSo3lzLQ0QWF9ejYU1m9a5drN61S3nrHdAb5YwxxlRfliCMMcYUyRLEb6Z6HYBHrN61i9W7dilXve0chDHGmCLZEYQxxpgiWYIwxhhTpFqfIGpTt+Ii8rKIpIjISp95jUTkGxFJdP829DLGiiYirURkloisEZFVInKTO7+m1ztCRBaKyC9uve9359foehcQkWARWSYin7vPa0u9k0TkVxFZLiKL3XnHXPdanSBqYbfirwKjCs2bBHynqh2A79znNUkucJuqdgEGAte773FNr3cWcLKq9gJ6A6Pc3gpqer0L3ASs8XleW+oNMFxVe/vc/3DMda/VCYJa1q24qv4A7C80+yzgNXf6NWBsZcYUaKq6U1WXutOHcb404qj59VZVTXOfhroPpYbXG0BEWgKnA//1mV3j612CY657bU8QfncrXoM1dfu/wv3bxON4AkZE4oHjgJ+pBfV2m1mWAynAN6paK+oNPAHcCeT7zKsN9QbnR8BMEVkiIuPdecdc99o+JrXf3Yqb6k1EooAPgZtV9ZBIUW99zaKqeUBvEWkAfCwi3T0OKeBE5AwgRVWXiMgwj8PxwmBV3SEiTYBvRGRteTZW248g/OmSvKbb7Y7ih/s3xeN4KpyIhOIkh7dU9SN3do2vdwFVPQjMxjn/VNPrPRgYIyJJOE3GJ4vIm9T8egOgqjvcvynAxzjN6Mdc99qeIKxbcae+l7vTlwOfehhLhRPnUOF/wBpV/bfPoppe71j3yAERqQOcAqylhtdbVe9W1ZaqGo/z//y9ql5KDa83gIhEikh0wTQwElhJOepe6++kFpE/4bRZFnQr/pC3EQWOiEwDhuF0AbwbuBf4BHgPaA1sBc5T1cInsqstERkCzAV+5bc26b/hnIeoyfXuiXNCMhjnh+B7qvqAiMRQg+vty21iul1Vz6gN9RaRtjhHDeCcPnhbVR8qT91rfYIwxhhTtNrexGSMMaYYliCMMcYUyRKEMcaYIlmCMMYYUyRLEMYYY4pkCcKYSiIiDUTkOq/jMMZfliCMqQRuz8ENgDIlCHHY/6nxhH3wjCmCiPzdHSfkWxGZJiK3i8hsEennLm/sdueAiMSLyFwRWeo+TnDnD3PHongb50a9h4F2bl/9j7nr3CEii0Rkhc+YDfHu+BXPAUuBViLyqoisdPv6v6Xy94ipjWp7Z33G/IGI9MXppuE4nP+RpcCSEoqkAKeqaqaIdACmAQV98Q8AuqvqZrc32e6q2tt9nZFAB3cdAaaLyEk4d7t2Aq5U1evceOJUtbtbrkEFVteYYlmCMOaPTgQ+VtV0ABEprX+uUOAZEekN5AEdfZYtVNXNxZQb6T6Wuc+jcBLGVmCLqi5w528C2orI08AXwMyyVceYY2MJwpiiFdUHTS6/NctG+My/Badvq17u8kyfZUdKeA0B/qmqL/5upnOkcbScqh4QkV7AacD1wPnAVX7VwphysHMQxvzRD8DZIlLH7R3zTHd+EtDXnR7ns359YKeq5gOX4XSQV5TDQLTP86+Bq9yxKhCROLcf/98RkcZAkKp+CEwG+hxTrYwpIzuCMKYQVV0qIu8Cy4EtOL3BAjwOvCcilwHf+xR5DvhQRM4DZlHMUYOq7hORH0VkJfClqt4hIl2A+e4ARmnApTjNVL7igFd8rma6u7x1NMYf1purMaUQkfuANFV93OtYjKlM1sRkjDGmSHYEYYwxpkh2BGGMMaZIliCMMcYUyRKEMcaYIlmCMMYYUyRLEMYYY4r0/5jE+++9Ce51AAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["td_nonlin_scaled = 100 * td_nonlin / ss  # scale all variables at once\n", "dC_nonlin = td_nonlin_scaled['C']\n", "\n", "plt.plot(dC[:50, 1], label='linear', linewidth=2.5)\n", "plt.plot(dC_nonlin[:50], label='nonlinear',  linestyle='--', linewidth=2.5)\n", "plt.title(r'Consumption response to 1% TFP news shock')\n", "plt.ylabel(r'% deviation from ss')\n", "plt.xlabel(r'quarters')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For those of you familiar with <PERSON><PERSON><PERSON>, these impulse responses are identical to what you could obtain by running the perfect foresight solver `simul`."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.2"}}, "nbformat": 4, "nbformat_minor": 2}