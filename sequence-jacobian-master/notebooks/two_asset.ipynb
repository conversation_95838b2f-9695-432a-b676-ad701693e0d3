{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Tutorial 4: A two-asset HANK model\n", "\n", "In this notebook we solve the two-asset HANK model from <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> (2021): \"Using the Sequence-Space Jacobian to Solve and Estimate Heterogeneous-Agent Models\" ([link to paper](https://www.bencebardoczy.com/publication/sequence-jacobian/sequence-jacobian.pdf)).\n", "\n", "New concepts:\n", "- **Solved block**: an extension of simple blocks that enables much more efficient DAG representations of large macro models.\n", "- **Re-using saved Jacobians**: as the cost of these computations becomes non-trivial, avoiding redundancy becomes key.\n", "- **Fine-tuning options**: how to access and modify various options for each (block, method) pair\n", "\n", "For more examples and information on the SSJ toolkit, please visit our [GitHub page](https://github.com/shade-econ/sequence-jacobian)."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "from sequence_jacobian import simple, solved, combine, create_model  # functions\n", "from sequence_jacobian import grids, hetblocks                       # modules"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1 Model description\n", "\n", "The household problem is characterized by the <PERSON><PERSON> equation\n", "$$\n", "\\begin{align} \\tag{1}\n", "V_t(e, b_{-}, a_{-}) = \\max_{c, b, a} &\\left\\{\\frac{c^{1-\\sigma}}{1-\\sigma} + \\beta \\mathbb{E}_t V_{t+1}(e', b, a) \\right\\}\n", "\\\\\n", "c + a + b &= z_t(e) + (1 + r_t^a)a_{-} + (1 + r_t^b)b_{-} - \\Psi(a, a_{-}) \n", "\\\\\n", "a &\\geq \\underline{a}, \\quad b \\geq \\underline{b},\n", "\\end{align}\n", "$$\n", "\n", "where $z_t(e)$ is labor income and the adjustment cost function is specified as\n", "\n", "$$\n", "\\Psi(a, a_{-}) =  \\frac{\\chi_1}{\\chi_2}\\left|\\frac{a - (1 + r_t^a) a_{-}}{(1 + r_{t}^a) a_{-} + \\chi_0}\\right|^{\\chi_2} \\left[(1 + r_t^a) a_{-} + \\chi_0 \\right],\n", "$$\n", "\n", "with $\\chi_0, \\chi_1 > 0$ and $\\chi_2 > 1.$ For the full description of the model, including the problems of the other agents, please see appendix B.3 of the paper."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We embed this household block in a New Keynesian model with sticky prices, sticky wages, and capital adjustment costs. Thanks to the **solved blocks** (in green), we can write a DAG for this model in just 3 unknowns $(r, w, Y)$ and 3 targets, asset market clearing, Fisher equation, wage Phillips curve."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2 Define solved blocks\n", "\n", "Solved blocks are miniature models embedded as blocks inside of our larger model. Like simple blocks, solved blocks correspond to aggregate equilibrium conditions: they map sequences of aggregate inputs directly into sequences of aggregate outputs. The difference is that in the case of simple blocks, this mapping has to be analytical, while solved blocks are designed to accommodate implicit relationships that can only be evaluated numerically. \n", "\n", "Such implicit mappings between variables become more common as macro complexity increases. Solved blocks are a valuable tool to simplify the DAG of large macro models."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1 Price setting (NKPC-p)\n", "The <PERSON> curve characterizes $(\\pi)$ conditional on $(Y, mc, r):$ \n", "\n", "$$\n", "\\log(1+\\pi_t) = \\kappa_p \\left(mc_t - \\frac{1}{\\mu_p} \\right) + \\frac{1}{1+r_{t+1}} \\frac{Y_{t+1}}{Y_t} \\log(1+\\pi_{t+1})\n", "$$\n", "\n", "Inflation shows up with two different time displacements, which means that inflation in any given period depends on the entire sequence of $(Y, mc, r)$. Simple blocks are not meant to represent such relationships. Instead, we write a function that returns the residual of the equation, and use the decorator `@solved` to make it into a `SolvedBlock`."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"scrolled": false}, "outputs": [], "source": ["@solved(unknowns={'pi': (-0.1, 0.1)}, targets=['nkpc'], solver=\"brentq\")\n", "def pricing_solved(pi, mc, r, Y, kappap, mup):\n", "    nkpc = kappap * (mc - 1/mup) + Y(+1) / Y * (1 + pi(+1)).apply(np.log) / \\\n", "           (1 + r(+1)) - (1 + pi).apply(np.log)\n", "    return nkpc"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When our routines encounter a solved block in `blocks`, they compute its Jacobian via the the implicit function theorem, as if it was a model on its own. Given the Jacobian, the rest of the code applies without modification. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Equity price (equity & dividend)\n", "The no arbitrage condition characterizes $(p)$ conditional on $(d, p, r).$\n", "\n", "$$\n", "p_t = \\frac{d_{t+1} + p_{t+1}}{1 + r_{t+1}}\n", "$$"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"scrolled": true}, "outputs": [], "source": ["@solved(unknowns={'p': (5, 15)}, targets=['equity'], solver=\"brentq\")\n", "def arbitrage_solved(div, p, r):\n", "    equity = div(+1) + p(+1) - p * (1 + r(+1))\n", "    return equity"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Investment with adjustment costs (prod)\n", "\n", "Sometimes multiple equilibrium conditions can be combined in a self-contained solved block. Investment subject to capital adjustment costs is such a case. In particular, we can use the following four equations to solve for $(K, Q)$ conditional on $(Y, w, r)$.\n", " \n", " - Production:\n", " \n", " $$\n", " Y_t = Z_t K_{t-1}^\\alpha N_t^{1-\\alpha}\n", " $$\n", " \n", " - Labor demand:\n", " \n", " $$\n", " w_t = (1-\\alpha)\\frac{Y_t}{N_t} mc_t\n", " $$\n", " \n", " - Investment equation:\n", "\n", "$$\n", "Q_t = 1 + \\frac{1}{\\delta \\epsilon_I}\\left(\\frac{K_t-K_{t-1}}{K_{t-1}}\\right)\n", "$$\n", "\n", "- Valuation equation\n", "\n", "$$\n", "(1+r_{t})Q_{t} = \\alpha Z_{t+1} \\left(\\frac{N_{t+1}}{K_t}\\right)^{1-\\alpha} mc_{t+1} - \\left[\\frac{K_{t+1}}{K_t} - (1-\\delta) + \\frac{1}{2\\delta \\epsilon_I}\\left(\\frac{K_{t+1} - K_t}{K_t}\\right)^2\\right] + \\frac{K_{t+1}}{K_t}Q_{t+1}\n", "$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Solved blocks that contain multiple simple blocks have to be initialized with the `CombinedBlock.solved` method instead of the decorator `@solved`."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["@simple\n", "def labor(Y, w, K, Z, alpha):\n", "    N = (Y / Z / K(-1) ** alpha) ** (1 / (1 - alpha))\n", "    mc = w * N / (1 - alpha) / Y\n", "    return N, mc\n", "\n", "\n", "@simple\n", "def investment(Q, K, r, N, mc, Z, delta, epsI, alpha):\n", "    inv = (K / K(-1) - 1) / (delta * epsI) + 1 - Q\n", "    val = alpha * Z(+1) * (N(+1) / K) ** (1 - alpha) * mc(+1) -\\\n", "        (K(+1) / K - (1 - delta) + (K(+1) / K - 1) ** 2 / (2 * delta * epsI)) +\\\n", "        K(+1) / K * Q(+1) - (1 + r(+1)) * Q\n", "    return inv, val\n", "\n", "\n", "production = combine([labor, investment])                              # create combined block\n", "production_solved = production.solved(unknowns={'Q': 1., 'K': 10.},    # turn it into solved block\n", "                                      targets=['inv', 'val'],\n", "                                      solver='broyden_custom')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3 Build DAGs\n", "\n", "One for transition dynamics (pictured above) and one for calibrating the steady state.\n", "\n", "### Step 1: Adapt HA block\n", "\n", "We developed an efficient backward iteration function to solve the <PERSON><PERSON> equation in (1). Although we view this as a contribution on its own, discussing the algorithm goes beyond the scope of this notebook. If you are interested in how we solve a two-asset model with convex portfolio-adjustment costs in discrete time, please see appendix E of the paper for a detailed description and `sequence_jacobian/hetblocks/hh_twoasset.py` for the implementation.\n", "\n", "Here, we take this generic two-asset model off the shelf, and embed it in our New Keynesian model with the help of two hetinputs."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def make_grids(bmax, amax, kmax, nB, nA, nK, nZ, rho_z, sigma_z):\n", "    b_grid = grids.agrid(amax=bmax, n=nB)\n", "    a_grid = grids.agrid(amax=amax, n=nA)\n", "    k_grid = grids.agrid(amax=kmax, n=nK)[::-1].copy()\n", "    e_grid, _, Pi = grids.markov_rouwenhorst(rho=rho_z, sigma=sigma_z, N=nZ)\n", "    return b_grid, a_grid, k_grid, e_grid, Pi\n", "\n", "\n", "def income(e_grid, tax, w, N):\n", "    z_grid = (1 - tax) * w * N * e_grid\n", "    return z_grid\n", "\n", "hh = hetblocks.hh_twoasset.hh\n", "hh_ext = hh.add_hetinputs([income, make_grids])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 2: Complete dynamic DAG with simple blocks\n", "\n", "We have set up all the blocks in the `sequence_jacobian/examples/two_asset.py` module. We omit the step-by-step discussion of these blocks since they should be familiar from the other model notebooks."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<SolvedBlock 'labor_to_investment_combined_solved'>\n", "<SolvedBlock 'pricing_solved'>\n", "<SimpleBlock 'wage'>\n", "<SimpleBlock 'taylor'>\n", "<SimpleBlock 'dividend'>\n", "<SolvedBlock 'arbitrage_solved'>\n", "<SimpleBlock 'share_value'>\n", "<SimpleBlock 'finance'>\n", "<SimpleBlock 'fiscal'>\n", "<HetBlock 'hh' with hetinput 'make_grids_marginal_cost_grid' and with hetoutput `adjustment_costs'>\n", "<SimpleBlock 'union'>\n", "<SimpleBlock 'mkt_clearing'>\n"]}], "source": ["import sequence_jacobian.examples.two_asset as m\n", "\n", "blocks = [hh_ext, production_solved, pricing_solved, arbitrage_solved,\n", "          m.dividend, m.taylor, m.fiscal, m.share_value,\n", "          m.finance, m.wage, m.union, m.mkt_clearing]\n", "\n", "hank = create_model(blocks, name='Two-Asset HANK')\n", "\n", "print(*hank.blocks, sep='\\n')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 3: Complete calibration DAG\n", "\n", "Analytical:\n", "- find TFP `Z` to hit target for output `Y`\n", "- find markup `mup` to hit target for total wealth `p + Bg`\n", "- find capital share `alpha` to hit target for capital `K`\n", "- find wage `w` to hit Phillips curve given zero inflation \n", "- find disutility of labor `vphi` to hit wage Phillips curve given a target for employment\n", "\n", "Numerical:\n", "- find discount factor `beta` to satisfy asset market clearing given an interest rate `r`\n", "- find adjustment cost scale `chi1` to hit target for average liquid wealth `Bh`"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Model 'Two-Asset HANK SS'>\n", "Inputs: ['beta', 'eis', 'chi0', 'chi1', 'chi2', 'N', 'bmax', 'amax', 'kmax', 'nB', 'nA', 'nK', 'nZ', 'rho_z', 'sigma_z', 'Y', 'K', 'r', 'tot_wealth', 'Bg', 'delta', 'muw', 'frisch', 'pi', 'kappap', 'epsI', 'rstar', 'phi', 'G', 'Bh', 'omega']\n"]}], "source": ["blocks_ss = [hh_ext, m.partial_ss, m.union_ss,\n", "             m.dividend, m.taylor, m.fiscal, m.share_value, m.finance, m.mkt_clearing]\n", "\n", "hank_ss = create_model(blocks_ss, name='Two-Asset HANK SS')\n", "\n", "print(hank_ss)\n", "print(f\"Inputs: {hank_ss.inputs}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4 Results\n", "\n", "We cover how to pass precomputed Jacobians to the main methods. This is useful when methods that need Jacobians are used repeatedly. These are\n", "- Solve methods: `solve_impulse_linear`, `solve_impulse_nonlinear`\n", "- Jacobian methods: `jacobian`, `solve_jacobian`\n", "\n", "### 4.1 Calibrate steady state\n", "\n", "Use the calibration DAG to internally calibrate the seven parameters (analytical + numerical). Evaluate the dynamic DAG at the resulting steady state `cali`. "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["calibration = {'Y': 1., 'N': 1.0, 'K': 10., 'r': 0.0125, 'rstar': 0.0125, 'tot_wealth': 14,\n", "               'delta': 0.02, 'pi': 0., 'kappap': 0.1, 'muw': 1.1, 'Bh': 1.04, 'Bg': 2.8,\n", "               'G': 0.2, 'eis': 0.5, 'frisch': 1., 'chi0': 0.25, 'chi2': 2, 'epsI': 4,\n", "               'omega': 0.005, 'kappaw': 0.1, 'phi': 1.5, 'nZ': 3, 'nB': 50, 'nA': 70, 'nK': 50,\n", "               'bmax': 50, 'amax': 4000, 'kmax': 1, 'rho_z': 0.966, 'sigma_z': 0.92}\n", "\n", "unknowns_ss = {'beta': 0.976, 'chi1': 6.5}\n", "targets_ss = {'asset_mkt': 0., 'B': 'Bh'}\n", "\n", "cali = hank_ss.solve_steady_state(calibration, unknowns_ss, targets_ss, solver='broyden_custom')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Verify solution, generate `ss` from dynamic DAG."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Liquid assets:  1.04\n", "Asset market clearing:  8.22e-13\n", "Goods market clearing (untargeted):  3.29e-08\n"]}], "source": ["ss =  hank.steady_state(cali)\n", "\n", "print(f\"Liquid assets: {ss['B']: 0.2f}\")\n", "print(f\"Asset market clearing: {ss['asset_mkt']: 0.2e}\")\n", "print(f\"Goods market clearing (untargeted): {ss['goods_mkt']: 0.2e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Linearized impulse responses\n", "\n", "As before, we can compute the general equilibrium Jacobians $G$ which is sufficient to map any shock into impulse responses. \n", "\n", "When the cost of computing a block Jacobian is non-trivial, it's a good idea to precompute it. We can supply block Jacobians for specific blocks via the `Js=` keyword argument. The precomputed Jacobians will only be used if they are **complete** (have all required inputs and outputs) and have the right **size** (truncation horizon)."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["exogenous = ['rstar', 'Z', 'G']\n", "unknowns = ['r', 'w', 'Y']\n", "targets = ['asset_mkt', 'fisher', 'wnkpc']\n", "T = 300\n", "\n", "J_ha = hh_ext.jacobian(ss, inputs=['N', 'r', 'ra', 'rb', 'tax', 'w'], T=T)\n", "G = hank.solve_jacobian(ss, unknowns, targets, exogenous, T=T, Js={'hh': J_ha})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The time saving from re-using the Jacobian of the household block is considerable."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 433 ms\n"]}], "source": ["%time G = hank.solve_jacobian(ss, unknowns, targets, exogenous, T=T, Js={'hh': J_ha})"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 4.94 s\n"]}], "source": ["%time G = hank.solve_jacobian(ss, unknowns, targets, exogenous, T=T)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that some block Jacobians may be precomputed even if others are changing. For example, we can re-use `J_ha` while evalutating the model likelihood for 100,000 draws of price and wage adjustment cost.\n", "\n", "When we're not planning to change any part of the model, it's even better to store the `H_U` directly. (To be precise, we store the LU-factorized version of the matrix, which facilitates operations with its inverse.) That way, we save time on computing and packing all the block Jacobians. "]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from sequence_jacobian.classes import FactoredJacobianDict\n", "\n", "H_U = hank.jacobian(ss, unknowns, targets, T=T, Js={'hh': J_ha})\n", "H_U_factored = FactoredJacobianDict(H_U, T)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 343 ms\n"]}], "source": ["%time G = hank.solve_jacobian(ss, unknowns, targets, exogenous, T=T, Js={'hh': J_ha}, H_U_factored=H_U_factored)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's plot some impulse responses:"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["rhos = np.array([0.2, 0.4, 0.6, 0.8])\n", "drstar = -0.0025 * rhos ** (np.arange(T)[:, np.newaxis])\n", "dY = 100 * G['Y']['rstar'] @ drstar\n", "\n", "plt.plot(dY[:21])\n", "plt.title(r'Output response to 25 bp monetary policy shocks with $\\rho=(0.2 ... 0.8)$')\n", "plt.xlabel('quarters')\n", "plt.ylabel('% deviation from ss')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Nonlinear impulse responses\n", "\n", "Let's compute the nonlinear impulse response for the $\\rho=0.6$ shock above.\n", "- Don't forget to use the saved Jacobian.\n", "- Note how to look up and change options specific to (block type, method) pairs."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'tol': 1e-08, 'maxit': 30, 'verbose': True}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["hank['pricing_solved'].solve_impulse_nonlinear_options"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By default, `SolvedBlock.solve_impulse_linear` prints the error in each iteration (`verbose=True`). Let's turn this off for the internal solved blocks."]}, {"cell_type": "code", "execution_count": 17, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solving Two-<PERSON><PERSON> HANK for ['r', 'w', 'Y'] to hit ['asset_mkt', 'fisher', 'wnkpc']\n", "On iteration 0\n", "   max error for asset_mkt is 3.92E-06\n", "   max error for fisher is 2.50E-03\n", "   max error for wnkpc is 4.72E-08\n", "On iteration 1\n", "   max error for asset_mkt is 2.66E-04\n", "   max error for fisher is 1.55E-06\n", "   max error for wnkpc is 2.15E-05\n", "On iteration 2\n", "   max error for asset_mkt is 7.56E-06\n", "   max error for fisher is 9.69E-08\n", "   max error for wnkpc is 6.57E-07\n", "On iteration 3\n", "   max error for asset_mkt is 4.01E-07\n", "   max error for fisher is 2.24E-09\n", "   max error for wnkpc is 1.64E-08\n", "On iteration 4\n", "   max error for asset_mkt is 2.20E-08\n", "   max error for fisher is 1.06E-10\n", "   max error for wnkpc is 7.46E-10\n", "On iteration 5\n", "   max error for asset_mkt is 1.23E-09\n", "   max error for fisher is 5.44E-12\n", "   max error for wnkpc is 3.72E-11\n"]}], "source": ["td_nonlin = hank.solve_impulse_nonlinear(ss, unknowns, targets, {\"rstar\": drstar[:, 2]},\n", "                                         Js={'hh': J_ha}, H_U_factored=H_U_factored,\n", "                                         options={'pricing_solved': {'verbose': False},\n", "                                                  'arbitrage_solved': {'verbose': False},\n", "                                                  'labor_to_investment_combined_solved': {'verbose': False}})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We see rapid convergence and mild nonlinearities in the solution."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["dY_nonlin = 100 * td_nonlin['Y']\n", "\n", "plt.plot(dY[:21, 2], label='linear', linestyle='-', linewidth=2.5)\n", "plt.plot(dY_nonlin[:21], label='nonlinear', linestyle='--', linewidth=2.5)\n", "plt.title(r'Consumption response to 1% monetary policy shock')\n", "plt.xlabel('quarters')\n", "plt.ylabel('% deviation from ss')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, we can compute the impulse response to a version of the shock scaled down to 10% of its original size."]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solving Two-<PERSON><PERSON> HANK for ['r', 'w', 'Y'] to hit ['asset_mkt', 'fisher', 'wnkpc']\n", "On iteration 0\n", "   max error for asset_mkt is 3.92E-06\n", "   max error for fisher is 2.50E-04\n", "   max error for wnkpc is 4.72E-08\n", "On iteration 1\n", "   max error for asset_mkt is 3.20E-06\n", "   max error for fisher is 1.63E-08\n", "   max error for wnkpc is 2.10E-06\n", "On iteration 2\n", "   max error for asset_mkt is 7.71E-08\n", "   max error for fisher is 2.53E-10\n", "   max error for wnkpc is 5.83E-09\n", "On iteration 3\n", "   max error for asset_mkt is 8.07E-10\n", "   max error for fisher is 1.50E-12\n", "   max error for wnkpc is 5.69E-11\n"]}], "source": ["td_nonlin = hank.solve_impulse_nonlinear(ss, unknowns, targets, {\"rstar\": 0.1 * drstar[:, 2]},\n", "                                         Js={'hh': J_ha},\n", "                                         options={'pricing_solved': {'verbose': False},\n", "                                                  'arbitrage_solved': {'verbose': False},\n", "                                                  'labor_to_investment_combined_solved': {'verbose': False}})"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["dY_nonlin = 100 * td_nonlin['Y']\n", "\n", "plt.plot(0.1*dY[:21, 2], label='linear', linestyle='-', linewidth=2.5)\n", "plt.plot(dY_nonlin[:21], label='nonlinear', linestyle='--', linewidth=2.5)\n", "plt.title(r'Consumption response to 0.1% monetary policy shock')\n", "plt.xlabel('quarters')\n", "plt.ylabel('% deviation from ss')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When the shock is this small, the linear and nonlinear impulse responses agree exactly. This is a good check, amid a highly complex model, that we didn't make any mistakes."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.3"}}, "nbformat": 4, "nbformat_minor": 2}