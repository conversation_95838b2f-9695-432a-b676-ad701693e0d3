[metadata]
name = sequence-jacobian
version = 1.0.0
author = Sequence-Jacobian Team
author_email = <EMAIL>
description = Sequence-Space Jacobian Methods for Solving and Estimating Heterogeneous Agent Models
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/shade-econ/sequence-jacobian
project_urls =
    Bug Tracker = https://github.com/shade-econ/sequence-jacobian/issues
classifiers =
    Programming Language :: Python :: 3
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent

[options]
package_dir =
    = src
packages = find:
python_requires = >=3.7
install_requires =
    numpy >= 1.19.2
    scipy >= 1.2
    numba >= 0.49

[options.packages.find]
where = src