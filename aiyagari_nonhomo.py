import numpy as np
from sequence_jacobian import grids, misc, interpolate, het, simple
from sequence_jacobian.blocks.combined_block import CombinedBlock
from scipy.optimize import root
import json

def hh_init(a_grid, y, r, eis):
    coh = (1 + r) * a_grid[np.newaxis, :] + y[:, np.newaxis]
    Va = (1 + r) * (0.1 * coh) ** (-1 / eis)
    return Va

@het(exogenous='Pi', policy='a', backward='Va', backward_init=hh_init)
def hh(Va_p, a_grid, y, r, beta, eis, epsilon, sigma, p_a, p_m, p_s):
    """Household block for non-homothetic Aiyagari model."""
    # Consumption index and expenditure
    def C_from_e(e, p_a, p_m, p_s, epsilon, sigma):
        return (e**(1-sigma) / (p_a**(1-sigma) * epsilon[0]**(1-sigma) +
                                p_m**(1-sigma) * epsilon[1]**(1-sigma) +
                                p_s**(1-sigma) * epsilon[2]**(1-sigma)))**(1/(1-sigma))

    def e_from_C(C, p_a, p_m, p_s, epsilon, sigma):
        return (p_a**(1-sigma) * (C**epsilon[0])**(1-sigma) +
                p_m**(1-sigma) * (C**epsilon[1])**(1-sigma) +
                p_s**(1-sigma) * (C**epsilon[2])**(1-sigma))**(1/(1-sigma))

    # Next-period consumption and marginal utility
    uc_nextgrid = beta * Va_p
    c_nextgrid = uc_nextgrid ** (-eis)
    e_nextgrid = e_from_C(c_nextgrid, p_a, p_m, p_s, epsilon, sigma)

    # Current period consumption and savings
    coh = (1 + r) * a_grid[np.newaxis, :] + y[:, np.newaxis]
    a = interpolate.interpolate_y(e_nextgrid + a_grid, coh, a_grid)
    misc.setmin(a, a_grid[0])
    e = coh - a
    misc.setmin(e, 1e-9)
    c = C_from_e(e, p_a, p_m, p_s, epsilon, sigma)
    
    # Consumption of each good
    c_a = (p_a/e)**(-sigma) * c**(epsilon[0]*(1-sigma))
    c_m = (p_m/e)**(-sigma) * c**(epsilon[1]*(1-sigma))
    c_s = (p_s/e)**(-sigma) * c**(epsilon[2]*(1-sigma))

    Va = (1 + r) * c ** (-1 / eis)
    return Va, a, c, c_a, c_m, c_s, e

@simple
def production_a(K_a, L_a, A_a, alpha_a):
    Y_a = A_a * K_a**(1-alpha_a) * L_a**alpha_a
    return Y_a

@simple
def production_m(K_m, L_m, A_m, alpha_m):
    Y_m = A_m * K_m**(1-alpha_m) * L_m**alpha_m
    return Y_m

@simple
def production_s(K_s, A_s, alpha_s, L_a, L_m):
    L_s = 1 - L_a - L_m
    Y_s = A_s * K_s**(1-alpha_s) * L_s**alpha_s
    return Y_s

@simple
def firm(Y_a, L_a, K_a, alpha_a, Y_m, L_m, K_m, alpha_m, Y_s, K_s, alpha_s, p_a, p_s, p_m, delta):
    L_s = 1 - L_a - L_m
    # Prices of inputs using manufacturing as numeraire
    W = p_m * alpha_m * Y_m / L_m
    R = p_m * (1 - alpha_m) * Y_m / K_m

    # Residuals for factor prices in other sectors
    w_a_res = W - p_a * alpha_a * Y_a / L_a
    r_a_res = R - p_a * (1 - alpha_a) * Y_a / K_a
    w_s_res = W - p_s * alpha_s * Y_s / L_s
    r_s_res = R - p_s * (1 - alpha_s) * Y_s / K_s

    # Price of investment good (assuming equal shares from each sector)
    p_I = (p_a + p_m + p_s) / 3
    
    # Real wage and interest rate for household (deflated by price of investment)
    w = W / p_I
    r = R / p_I - delta

    return w, r, p_I, w_a_res, r_a_res, w_s_res, r_s_res

@simple
def mkt_clearing(K_a, K_m, K_s, L_a, L_m, A, Y_a, Y_m, Y_s, C_A, C_M, C_S, p_I, delta):
    L_s = 1 - L_a - L_m
    # Aggregate capital and labor
    K = K_a + K_m + K_s
    L = L_a + L_m + L_s

    # Total investment
    I = delta * K

    # Market clearing for each sector's good
    mkt_a = Y_a - C_A - I / 3
    mkt_m = Y_m - C_M - I / 3
    mkt_s = Y_s - C_S - I / 3
    
    # Asset and labor market clearing
    asset_mkt = A - p_I * K
    labor_mkt = L - 1
    
    return asset_mkt, labor_mkt, mkt_a, mkt_m, mkt_s, I, L_s

def make_grids(rho_e, sd_e, n_e, min_a, max_a, n_a):
    e_grid, _, Pi = grids.markov_rouwenhorst(rho_e, sd_e, n_e)
    a_grid = grids.asset_grid(min_a, max_a, n_a)
    return e_grid, Pi, a_grid

def income(w, e_grid):
    y = w * e_grid
    return y

hh_extended = hh.add_hetinputs([income, make_grids])

aiyagari_model = CombinedBlock([hh_extended, production_a, production_m, production_s, firm, mkt_clearing], name="Aiyagari-Nonhomothetic")

calibration = {
    # Household
    'beta': 0.96,
    'eis': 0.5,
    'epsilon': np.array([0.05, 1.0, 1.2]),
    'sigma': 0.5,
    'min_a': 0.0,
    'max_a': 200.0,
    'n_a': 500,
    'rho_e': 0.9,
    'sd_e': 0.2,
    'n_e': 7,

    # Firms
    'A_a': 1.0,
    'A_m': 1.0,
    'A_s': 1.0,
    'alpha_a': 0.30,
    'alpha_m': 0.33,
    'alpha_s': 0.36,
    'delta': 0.05,

    # Prices (p_m is numeraire)
    'p_m': 1.0,
}

def excess_demand(x, calibration, model):
    # Unpack and scale the variables to be solved for
    L_a, L_m, k_a, k_m, k_s, p_a, p_s = x
    K_a, K_m, K_s = 10 * k_a, 10 * k_m, 10 * k_s  # Scale up capital variables
    L_s = 1 - L_a - L_m  # Labor market clearing

    # Impose bounds to prevent solver from entering nonsensical regions
    if not (0.01 < L_a < 0.99 and 0.01 < L_m < 0.99 and 0.01 < L_s < 0.99 and 
            K_a > 0.1 and K_m > 0.1 and K_s > 0.1 and 
            p_a > 0.1 and p_s > 0.1):
        return np.full(7, 1e9)

    unknowns = {
        'L_a': L_a, 'L_m': L_m, 'L_s': L_s,
        'K_a': K_a, 'K_m': K_m, 'K_s': K_s,
        'p_a': p_a, 'p_s': p_s
    }

    try:
        ss = model.steady_state({**calibration, **unknowns})
        # Scale the asset market residual to be of a similar order of magnitude to other residuals
        residuals = np.array([
            ss['asset_mkt'] / 10.0,
            ss['w_a_res'],
            ss['r_a_res'],
            ss['w_s_res'],
            ss['r_s_res'],
            ss['mkt_a'],
            ss['mkt_s']
        ])
        return residuals
    except (ValueError, KeyError):
        return np.full(7, 1e9)

# Initial guess for the scaled variables
initial_guess = np.array([0.2, 0.3, 1.0, 1.0, 1.5, 1.5, 0.8])  # L_a, L_m, k_a, k_m, k_s, p_a, p_s

sol = root(excess_demand, initial_guess, args=(calibration, aiyagari_model), method='lm')

if sol.success:
    # Resolve for the full steady state with the unscaled solution
    L_a, L_m, k_a, k_m, k_s, p_a, p_s = sol.x
    K_a, K_m, K_s = 10 * k_a, 10 * k_m, 10 * k_s
    L_s = 1 - L_a - L_m
    final_unknowns = {
        'L_a': L_a, 'L_m': L_m, 'L_s': L_s,
        'K_a': K_a, 'K_m': K_m, 'K_s': K_s,
        'p_a': p_a, 'p_s': p_s
    }
    ss = aiyagari_model.steady_state({**calibration, **final_unknowns})

    ss_dict = {k: v.tolist() if isinstance(v, np.ndarray) else v for k, v in ss.items()}

    with open("steady_state.json", "w") as f:
        json.dump(ss_dict, f, indent=4)

    print("Steady-state solution found and saved to steady_state.json")
    print(ss)
else:
    print("Solver failed to find a solution.")
    print("Message from solver:", sol.message)
    print("Final (failed) guess:", sol.x)
    print("Residuals at final guess:", sol.fun)
