import numpy as np
from sequence_jacobian import grids, misc, interpolate, het, simple
from sequence_jacobian.blocks.combined_block import CombinedBlock
from scipy.optimize import root

def hh_init(a_grid, y, r, eis):
    coh = (1 + r) * a_grid[np.newaxis, :] + y[:, np.newaxis]
    Va = (1 + r) * (0.1 * coh) ** (-1 / eis)
    return Va

@het(exogenous='Pi', policy='a', backward='Va', backward_init=hh_init)
def hh(Va_p, a_grid, y, r, beta, eis, epsilon, sigma, p_a, p_m, p_s):
    """Household block for non-homothetic Aiyagari model."""
    # Consumption index and expenditure
    def C_from_e(e, p_a, p_m, p_s, epsilon, sigma):
        return (e**(1-sigma) / (p_a**(1-sigma) * epsilon[0]**(1-sigma) +
                                p_m**(1-sigma) * epsilon[1]**(1-sigma) +
                                p_s**(1-sigma) * epsilon[2]**(1-sigma)))**(1/(1-sigma))

    def e_from_C(C, p_a, p_m, p_s, epsilon, sigma):
        return (p_a**(1-sigma) * (C**epsilon[0])**(1-sigma) +
                p_m**(1-sigma) * (C**epsilon[1])**(1-sigma) +
                p_s**(1-sigma) * (C**epsilon[2])**(1-sigma))**(1/(1-sigma))

    # Next-period consumption and marginal utility
    uc_nextgrid = beta * Va_p
    c_nextgrid = uc_nextgrid ** (-eis)
    e_nextgrid = e_from_C(c_nextgrid, p_a, p_m, p_s, epsilon, sigma)

    # Current period consumption and savings
    coh = (1 + r) * a_grid[np.newaxis, :] + y[:, np.newaxis]
    a = interpolate.interpolate_y(e_nextgrid + a_grid, coh, a_grid)
    misc.setmin(a, a_grid[0])
    e = coh - a
    c = C_from_e(e, p_a, p_m, p_s, epsilon, sigma)
    
    # Consumption of each good
    c_a = (p_a/e)**(-sigma) * c**(epsilon[0]*(1-sigma))
    c_m = (p_m/e)**(-sigma) * c**(epsilon[1]*(1-sigma))
    c_s = (p_s/e)**(-sigma) * c**(epsilon[2]*(1-sigma))

    Va = (1 + r) * c ** (-1 / eis)
    return Va, a, c, c_a, c_m, c_s, e

@simple
def production_a(K_a, L_a, A_a, alpha_a):
    Y_a = A_a * abs(K_a)**(1-alpha_a) * abs(L_a)**alpha_a
    return Y_a

@simple
def production_m(K_m, L_m, A_m, alpha_m):
    Y_m = A_m * abs(K_m)**(1-alpha_m) * abs(L_m)**alpha_m
    return Y_m

@simple
def production_s(K_s, L_s, A_s, alpha_s):
    Y_s = A_s * abs(K_s)**(1-alpha_s) * abs(L_s)**alpha_s
    return Y_s

@simple
def prices(Y_a, L_a, K_a, alpha_a):
    w = alpha_a * Y_a / abs(L_a)
    r = (1-alpha_a) * Y_a / abs(K_a)
    return w, r

@simple
def firm_residuals(Y_m, L_m, K_m, alpha_m, Y_s, L_s, K_s, alpha_s, w, r):
    w_m_res = w - alpha_m * Y_m / abs(L_m)
    r_m_res = r - (1-alpha_m) * Y_m / abs(K_m)
    w_s_res = w - alpha_s * Y_s / abs(L_s)
    r_s_res = r - (1-alpha_s) * Y_s / abs(K_s)
    return w_m_res, r_m_res, w_s_res, r_s_res

@simple
def mkt_clearing(K_a, K_m, K_s, L_a, L_m, L_s, A, Y_a, Y_m, Y_s, C_A, C_M, C_S, delta):
    K = K_a + K_m + K_s
    I = delta * K
    L = L_a + L_m + L_s
    goods_mkt = Y_a + Y_m + Y_s - C_A - C_M - C_S - I
    asset_mkt = K - A
    labor_mkt = L - 1
    return goods_mkt, asset_mkt, labor_mkt, I

def make_grids(rho_e, sd_e, n_e, min_a, max_a, n_a):
    e_grid, _, Pi = grids.markov_rouwenhorst(rho_e, sd_e, n_e)
    a_grid = grids.asset_grid(min_a, max_a, n_a)
    return e_grid, Pi, a_grid

def income(w, e_grid):
    y = w * e_grid
    return y

hh_extended = hh.add_hetinputs([income, make_grids])

aiyagari_model = CombinedBlock([hh_extended, production_a, production_m, production_s, prices, firm_residuals, mkt_clearing], name="Aiyagari-Nonhomothetic")

calibration = {
    # Household
    'beta': 0.96,
    'eis': 0.5,
    'epsilon': np.array([0.5, 1.0, 1.5]),
    'sigma': 0.5,
    'min_a': 0.0,
    'max_a': 200.0,
    'n_a': 1000,
    'rho_e': 0.9,
    'sd_e': 0.2,
    'n_e': 7,

    # Firms
    'A_a': 1.0,
    'A_m': 1.0,
    'A_s': 1.0,
    'alpha_a': 0.30,
    'alpha_m': 0.33,
    'alpha_s': 0.36,
    'delta': 0.05,

    # Prices
    'p_a': 1.0,
    'p_m': 1.0,
    'p_s': 1.0,
}

def excess_demand(x):
    L_a, L_m, L_s, K_a, K_m, K_s = x
    unknowns = {
        'L_a': L_a,
        'L_m': L_m,
        'L_s': L_s,
        'K_a': K_a,
        'K_m': K_m,
        'K_s': K_s,
    }
    ss = aiyagari_model.steady_state({**calibration, **unknowns})
    return np.array([
        ss['asset_mkt'],
        ss['labor_mkt'],
        ss['w_m_res'],
        ss['r_m_res'],
        ss['w_s_res'],
        ss['r_s_res'],
    ])

x0 = np.array([0.33, 0.33, 0.34, 12.0, 12.0, 12.0])
sol = root(excess_demand, x0, method='krylov')

ss = aiyagari_model.steady_state({**calibration, **dict(zip(['L_a', 'L_m', 'L_s', 'K_a', 'K_m', 'K_s'], sol.x))})
import json

# Convert SteadyStateDict to a regular dict for json serialization
ss_dict = {k: v.tolist() if isinstance(v, np.ndarray) else v for k, v in ss.items()}

# Save to a json file
with open("steady_state.json", "w") as f:
    json.dump(ss_dict, f, indent=4)

print("Steady-state results saved to steady_state.json")
print(ss)
