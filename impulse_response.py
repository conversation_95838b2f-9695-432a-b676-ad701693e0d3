import numpy as np
import matplotlib.pyplot as plt
import json
from sequence_jacobian import create_model, SteadyStateDict, grids
from Aiyagari_Nonhomo import aiyagari_model, hh_extended, production_a, production_m, production_s, firm, mkt_clearing, hh

# Load the steady-state solution
with open("steady_state.json", "r") as f:
    ss_dict = json.load(f)

# Convert to SteadyStateDict and add required household block outputs
ss = SteadyStateDict(ss_dict)
ss['e_grid'], _, ss['Pi'] = grids.markov_rouwenhorst(ss['rho_e'], ss['sd_e'], ss['n_e'])
ss['a_grid'] = grids.asset_grid(ss['min_a'], ss['max_a'], ss['n_a'])
ss['y'] = ss['w'] * ss['e_grid']

hh_ss = hh.steady_state(ss)
ss['a'] = hh_ss.internals['hh']['a']
ss['Va'] = hh_ss.internals['hh']['Va']
ss['Dbeg'] = hh_ss.internals['hh']['Dbeg']
ss['c'] = hh_ss.internals['hh']['c']
ss['c_a'] = hh_ss.internals['hh']['c_a']
ss['c_m'] = hh_ss.internals['hh']['c_m']
ss['c_s'] = hh_ss.internals['hh']['c_s']
ss['e'] = hh_ss.internals['hh']['e']

# Define the model for impulse response analysis
model = create_model([hh_extended, production_a, production_m, production_s, firm, mkt_clearing], name="Aiyagari-Nonhomothetic-IR")

# Define unknowns and targets for GE Jacobian calculation
unknowns = ['L_a', 'L_m', 'K_a', 'K_m', 'K_s', 'p_a', 'p_s']
targets = ['asset_mkt', 'w_a_res', 'r_a_res', 'w_s_res', 'r_s_res', 'mkt_a', 'mkt_s']
exogenous = ['A_a', 'A_m', 'A_s']

# Compute the general equilibrium Jacobian
T = 200
G = model.solve_jacobian(ss, unknowns, targets, exogenous, T=T)

# Define the shock: 1% negative TFP shock with persistence 0.8
shock = np.zeros((T, len(exogenous)))
persistence = 0.8
shock_size = -0.01
for t in range(T):
    shock[t, :] = (persistence**t) * shock_size

# Compute impulse responses
d = {k: shock[:, i] for i, k in enumerate(exogenous)}
td = model.solve_impulse_linear(ss, unknowns, targets, d)

# Plot the results
fig, axes = plt.subplots(3, 2, figsize=(12, 12))
fig.suptitle('Impulse Responses to a 1% Negative TFP Shock')

# Output
axes[0, 0].plot(td['Y_a'], label='Agriculture')
axes[0, 0].plot(td['Y_m'], label='Manufacturing')
axes[0, 0].plot(td['Y_s'], label='Services')
axes[0, 0].set_title('Output')
axes[0, 0].set_ylabel('% dev from ss')
axes[0, 0].legend()

# Consumption
axes[0, 1].plot(td['C'], label='Aggregate Consumption')
axes[0, 1].set_title('Consumption')
axes[0, 1].legend()

# Capital
axes[1, 0].plot(td['K_a'], label='Agriculture')
axes[1, 0].plot(td['K_m'], label='Manufacturing')
axes[1, 0].plot(td['K_s'], label='Services')
axes[1, 0].set_title('Capital')
axes[1, 0].set_ylabel('% dev from ss')
axes[1, 0].legend()

# Labor
axes[1, 1].plot(td['L_a'], label='Agriculture')
axes[1, 1].plot(td['L_m'], label='Manufacturing')
axes[1, 1].plot(td['L_s'], label='Services')
axes[1, 1].set_title('Labor')
axes[1, 1].set_ylabel('% dev from ss')
axes[1, 1].legend()

# Prices
axes[2, 0].plot(td['p_a'], label='p_a')
axes[2, 0].plot(td['p_s'], label='p_s')
axes[2, 0].set_title('Prices')
axes[2, 0].set_ylabel('% dev from ss')
axes[2, 0].legend()

# Interest rate and wage
axes[2, 1].plot(td['r'], label='Interest Rate (r)')
axes[2, 1].plot(td['w'], label='Wage (w)')
axes[2, 1].set_title('Interest Rate and Wage')
axes[2, 1].set_ylabel('% dev from ss')
axes[2, 1].legend()


for ax in axes.flatten():
    ax.grid(True)

plt.tight_layout(rect=[0, 0, 1, 0.96])
plt.savefig('impulse_responses.png')
plt.show()